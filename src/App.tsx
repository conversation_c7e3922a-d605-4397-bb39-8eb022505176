import React, { useEffect } from 'react';
import { RouterProvider } from 'react-router-dom';
import { router } from '@/app/router';
import { ToastProvider } from '@/shared/providers/ToastProvider';
import { indexedDBService } from '@/infrastructure/storage/indexedDBService';

function App() {
  useEffect(() => {
    // 初始化IndexedDB持久化存储
    const initializeStorage = async () => {
      try {
        await indexedDBService.initialize();
        console.log('应用存储初始化完成');
      } catch (error) {
        console.error('应用存储初始化失败:', error);
        // 可以在这里添加用户提示或降级处理
      }
    };

    initializeStorage();

    // 清理函数
    return () => {
      indexedDBService.close();
    };
  }, []);

  return (
    <ToastProvider>
      <RouterProvider router={router} />
    </ToastProvider>
  );
}

export default App;
