import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useNutritionStore } from '@/domains/nutrition/stores/nutritionStore';
import { useUserStore } from '@/domains/user/stores/userStore';
import { geminiService } from '@/infrastructure/ai/geminiService';
import { FoodRecord as FoodRecordType, MealType } from '@/shared/types';
import BottomNavigation from '@/shared/components/navigation/BottomNavigation';
import { formatDate } from '@/shared/utils';
// import anime from 'animejs/lib/anime.es.js';

// 餐次配置
const MEAL_CONFIG = {
  breakfast: { 
    label: '早餐', 
    icon: '🌅', 
    timeRange: '6:00-10:00',
    color: 'text-orange-600 bg-orange-50 border-orange-200'
  },
  lunch: { 
    label: '午餐', 
    icon: '☀️', 
    timeRange: '11:00-14:00',
    color: 'text-green-600 bg-green-50 border-green-200'
  },
  dinner: { 
    label: '晚餐', 
    icon: '🌙', 
    timeRange: '17:00-21:00',
    color: 'text-blue-600 bg-blue-50 border-blue-200'
  },
  snack: { 
    label: '加餐', 
    icon: '🍎', 
    timeRange: '随时',
    color: 'text-purple-600 bg-purple-50 border-purple-200'
  }
};

// 获取当前餐次
const getCurrentMealType = (): MealType => {
  const hour = new Date().getHours();
  if (hour >= 6 && hour < 11) return 'breakfast';
  if (hour >= 11 && hour < 15) return 'lunch';
  if (hour >= 17 && hour < 22) return 'dinner';
  return 'snack';
};

const FoodRecordPage: React.FC = () => {
  const navigate = useNavigate();
  const { profile } = useUserStore();
  const {
    getDailyFoodRecords,
    addDetailedFoodRecord,
    updateFoodRecord,
    deleteFoodRecord
  } = useNutritionStore();

  // 状态管理
  const [currentDate, setCurrentDate] = useState(new Date());
  const [activeView, setActiveView] = useState<'list' | 'add-text' | 'add-image'>('list');
  const [selectedMeal, setSelectedMeal] = useState<MealType>(getCurrentMealType());
  
  // AI识别状态
  const [textInput, setTextInput] = useState('');
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 编辑状态
  const [editingRecord, setEditingRecord] = useState<FoodRecordType | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editForm, setEditForm] = useState({
    name: '',
    weight: 0,
    calories: 0,
    mealType: 'breakfast' as MealType
  });

  // 获取当日食物记录
  const dailyRecords = getDailyFoodRecords(currentDate);

  // 页面动画初始化 (暂时禁用)
  useEffect(() => {
    // TODO: 修复anime.js导入问题后重新启用动画
    // if (activeView === 'list') {
    //   const timelineItems = document.querySelectorAll('.food-timeline-item');
    //   if (timelineItems.length > 0) {
    //     anime({
    //       targets: '.food-timeline-item',
    //       translateY: [20, 0],
    //       opacity: [0, 1],
    //       delay: anime.stagger(100),
    //       duration: 600,
    //       easing: 'easeOutQuad'
    //     });
    //   }
    // }
  }, [dailyRecords, activeView]);

  // 处理文字识别
  const handleTextAnalysis = async () => {
    if (!textInput.trim()) {
      setError('请输入食物描述');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      const result = await geminiService.analyzeTextFood(textInput);
      
      if (!result.foods || result.foods.length === 0) {
        setError('未能从文字中识别到食物信息，请尝试更详细的描述');
        return;
      }

      // 转换为食物记录并保存
      for (const food of result.foods) {
        const foodRecord: Omit<FoodRecordType, 'id' | 'createdAt' | 'updatedAt'> = {
          name: food.name,
          weight: food.weight,
          calories: food.calories,
          mealType: selectedMeal,
          recordedAt: new Date(),
          nutrition: {
            protein: food.nutrition?.protein || 0,
            fat: food.nutrition?.fat || 0,
            carbs: food.nutrition?.carbs || 0,
            fiber: food.nutrition?.fiber || 0,
            sugar: food.nutrition?.sugar || 0
          },
          aiRecognition: {
            confidence: food.confidence,
            method: 'text',
            originalInput: textInput
          },
          isEdited: false
        };

        addDetailedFoodRecord(currentDate, foodRecord);
      }

      // 重置状态并返回列表
      setTextInput('');
      setActiveView('list');

    } catch (error) {
      console.error('文字分析失败:', error);
      setError('文字分析失败，请检查网络连接后重试');
    } finally {
      setIsProcessing(false);
    }
  };

  // 处理图片选择
  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setSelectedImage(file);
    setImagePreview(URL.createObjectURL(file));
    setError(null);
  };

  // 处理图片分析
  const handleImageAnalysis = async () => {
    if (!selectedImage) return;

    setIsProcessing(true);
    setError(null);

    try {
      const result = await geminiService.recognizeFood(selectedImage);
      
      if (!result.foods || result.foods.length === 0) {
        setError('未能识别到食物，请确保图片清晰并包含食物');
        return;
      }

      // 转换为食物记录并保存
      for (const food of result.foods) {
        const foodRecord: Omit<FoodRecordType, 'id' | 'createdAt' | 'updatedAt'> = {
          name: food.name,
          weight: food.weight,
          calories: food.calories,
          mealType: selectedMeal,
          recordedAt: new Date(),
          nutrition: {
            protein: food.nutrition?.protein || 0,
            fat: food.nutrition?.fat || 0,
            carbs: food.nutrition?.carbs || 0,
            fiber: food.nutrition?.fiber || 0,
            sugar: food.nutrition?.sugar || 0
          },
          aiRecognition: {
            confidence: food.confidence,
            method: 'image',
            originalInput: selectedImage.name
          },
          isEdited: false
        };

        addDetailedFoodRecord(currentDate, foodRecord);
      }

      // 重置状态并返回列表
      setSelectedImage(null);
      setImagePreview(null);
      setActiveView('list');

    } catch (error) {
      console.error('图片分析失败:', error);
      setError('图片分析失败，请检查网络连接后重试');
    } finally {
      setIsProcessing(false);
    }
  };

  // 删除食物记录
  const handleDeleteRecord = (recordId: string) => {
    if (window.confirm('确定要删除这条食物记录吗？')) {
      // 直接删除数据 (动画暂时禁用)
      deleteFoodRecord(currentDate, recordId);

      // TODO: 修复anime.js导入问题后重新启用删除动画
      // const targetElement = document.querySelector(`[data-record-id="${recordId}"]`);
      // if (targetElement) {
      //   anime({
      //     targets: `[data-record-id="${recordId}"]`,
      //     translateX: [-300],
      //     opacity: [0],
      //     duration: 400,
      //     easing: 'easeInQuad',
      //     complete: () => {
      //       deleteFoodRecord(currentDate, recordId);
      //     }
      //   });
      // }
    }
  };

  // 编辑食物记录
  const handleEditRecord = (record: FoodRecordType) => {
    setEditingRecord(record);
    setEditForm({
      name: record.name,
      weight: record.weight,
      calories: record.calories,
      mealType: record.mealType
    });
    setShowEditModal(true);
  };

  // 保存编辑
  const handleSaveEdit = () => {
    if (!editingRecord) return;

    updateFoodRecord(currentDate, editingRecord.id, {
      name: editForm.name,
      weight: editForm.weight,
      calories: editForm.calories,
      mealType: editForm.mealType
    });

    setShowEditModal(false);
    setEditingRecord(null);
  };

  // 渲染文字输入视图
  const renderTextInputView = () => {
    return (
      <div className="space-y-6">
        {/* 餐次选择 */}
        <div className="bg-white rounded-xl p-4 shadow-sm border border-slate-200">
          <h3 className="font-bold text-slate-800 mb-3">选择餐次</h3>
          <div className="grid grid-cols-2 gap-2">
            {(Object.keys(MEAL_CONFIG) as MealType[]).map((mealType) => {
              const config = MEAL_CONFIG[mealType];
              return (
                <button
                  key={mealType}
                  onClick={() => setSelectedMeal(mealType)}
                  className={`p-3 rounded-lg border-2 transition-all ${
                    selectedMeal === mealType
                      ? config.color + ' border-current'
                      : 'border-slate-200 hover:border-slate-300'
                  }`}
                >
                  <div className="text-lg mb-1">{config.icon}</div>
                  <div className="text-sm font-medium">{config.label}</div>
                </button>
              );
            })}
          </div>
        </div>

        {/* 文字输入 */}
        <div className="bg-white rounded-xl p-4 shadow-sm border border-slate-200">
          <h3 className="font-bold text-slate-800 mb-3">描述食物</h3>
          <textarea
            value={textInput}
            onChange={(e) => setTextInput(e.target.value)}
            placeholder="例如：一碗白米饭，一个苹果，200ml牛奶..."
            className="textarea textarea-bordered w-full h-32 resize-none"
            disabled={isProcessing}
          />
          <div className="text-xs text-slate-500 mt-2">
            💡 提示：描述越详细，AI识别越准确
          </div>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="alert alert-error">
            <span>{error}</span>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex gap-3">
          <button
            onClick={() => setActiveView('list')}
            className="btn btn-ghost flex-1"
            disabled={isProcessing}
          >
            取消
          </button>
          <button
            onClick={handleTextAnalysis}
            className="btn btn-primary flex-1"
            disabled={isProcessing || !textInput.trim()}
          >
            {isProcessing ? (
              <>
                <span className="loading loading-spinner loading-sm"></span>
                分析中...
              </>
            ) : (
              '开始识别'
            )}
          </button>
        </div>
      </div>
    );
  };

  // 渲染图片输入视图
  const renderImageInputView = () => {
    return (
      <div className="space-y-6">
        {/* 餐次选择 */}
        <div className="bg-white rounded-xl p-4 shadow-sm border border-slate-200">
          <h3 className="font-bold text-slate-800 mb-3">选择餐次</h3>
          <div className="grid grid-cols-2 gap-2">
            {(Object.keys(MEAL_CONFIG) as MealType[]).map((mealType) => {
              const config = MEAL_CONFIG[mealType];
              return (
                <button
                  key={mealType}
                  onClick={() => setSelectedMeal(mealType)}
                  className={`p-3 rounded-lg border-2 transition-all ${
                    selectedMeal === mealType
                      ? config.color + ' border-current'
                      : 'border-slate-200 hover:border-slate-300'
                  }`}
                >
                  <div className="text-lg mb-1">{config.icon}</div>
                  <div className="text-sm font-medium">{config.label}</div>
                </button>
              );
            })}
          </div>
        </div>

        {/* 图片选择 */}
        <div className="bg-white rounded-xl p-4 shadow-sm border border-slate-200">
          <h3 className="font-bold text-slate-800 mb-3">选择图片</h3>

          {!imagePreview ? (
            <label className="block">
              <input
                type="file"
                accept="image/*"
                onChange={handleImageSelect}
                className="hidden"
                disabled={isProcessing}
              />
              <div className="border-2 border-dashed border-slate-300 rounded-lg p-8 text-center hover:border-slate-400 transition-colors cursor-pointer">
                <div className="text-4xl mb-2">📷</div>
                <div className="font-medium text-slate-700">点击选择图片</div>
                <div className="text-sm text-slate-500 mt-1">支持拍照或从相册选择</div>
              </div>
            </label>
          ) : (
            <div className="space-y-3">
              <div className="relative">
                <img
                  src={imagePreview}
                  alt="选择的食物图片"
                  className="w-full h-48 object-cover rounded-lg border border-slate-200"
                />
                <button
                  onClick={() => {
                    setSelectedImage(null);
                    setImagePreview(null);
                  }}
                  className="absolute top-2 right-2 btn btn-circle btn-sm bg-red-500 text-white hover:bg-red-600 border-none"
                  disabled={isProcessing}
                >
                  ✕
                </button>
              </div>
              <label className="block">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageSelect}
                  className="hidden"
                  disabled={isProcessing}
                />
                <button className="btn btn-ghost btn-sm w-full" disabled={isProcessing}>
                  重新选择图片
                </button>
              </label>
            </div>
          )}
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="alert alert-error">
            <span>{error}</span>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex gap-3">
          <button
            onClick={() => setActiveView('list')}
            className="btn btn-ghost flex-1"
            disabled={isProcessing}
          >
            取消
          </button>
          <button
            onClick={handleImageAnalysis}
            className="btn btn-primary flex-1"
            disabled={isProcessing || !selectedImage}
          >
            {isProcessing ? (
              <>
                <span className="loading loading-spinner loading-sm"></span>
                识别中...
              </>
            ) : (
              '开始识别'
            )}
          </button>
        </div>
      </div>
    );
  };

  // 渲染时间轴视图
  const renderTimelineView = () => {
    const mealTypes: MealType[] = ['breakfast', 'lunch', 'dinner', 'snack'];
    
    return (
      <div className="space-y-6">
        {/* 简化日期选择器 */}
        <div className="flex items-center justify-center bg-white rounded-xl p-3 shadow-sm border border-slate-200 mb-1">
          <div className="flex items-center gap-3">
            <button
              onClick={() => setCurrentDate(new Date(currentDate.getTime() - 24 * 60 * 60 * 1000))}
              className="btn btn-ghost btn-sm w-8 h-8 min-h-8 p-0"
            >
              ←
            </button>
            <div className="text-center min-w-[100px]">
              <div className="font-bold text-slate-800 text-base">
                {formatDate(currentDate, 'MM月dd日')}
              </div>
              <div className="text-xs text-slate-500 -mt-1">
                {formatDate(currentDate, 'EEEE')}
              </div>
            </div>
            <button
              onClick={() => setCurrentDate(new Date(currentDate.getTime() + 24 * 60 * 60 * 1000))}
              className="btn btn-ghost btn-sm w-8 h-8 min-h-8 p-0"
              disabled={formatDate(currentDate, 'yyyy-MM-dd') >= formatDate(new Date(), 'yyyy-MM-dd')}
            >
              →
            </button>
          </div>
        </div>

        {/* 左对齐时间轴 */}
        <div className="space-y-4">
          {mealTypes.map((mealType, index) => {
            const mealRecords = dailyRecords?.mealRecords[mealType] || [];
            const mealConfig = MEAL_CONFIG[mealType];
            const totalCalories = mealRecords.reduce((sum, record) => sum + record.calories, 0);

            return (
              <div key={mealType} className={`food-timeline-item ${index === 0 ? 'mt-4' : ''}`}>
                {/* 餐次标题行 */}
                <div className="flex items-center gap-3 mb-3">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${mealConfig.color} border-2 flex-shrink-0`}>
                    <span className="text-lg">{mealConfig.icon}</span>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <h3 className="font-bold text-slate-800 text-lg">{mealConfig.label}</h3>
                        <div className={`badge ${mealConfig.color} font-medium text-xs`}>
                          {mealConfig.timeRange}
                        </div>
                      </div>
                      <div className="text-sm text-slate-500 font-medium">
                        {totalCalories} kcal
                      </div>
                    </div>
                  </div>
                </div>

                {/* 食物记录卡片区域 */}
                <div className="ml-13 mb-6">
                  {mealRecords.length === 0 ? (
                    <div className="bg-white rounded-lg border border-slate-200 p-6 text-center text-slate-400">
                      <div className="text-3xl mb-2">🍽️</div>
                      <div className="text-sm">暂无记录</div>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {mealRecords.map((record) => (
                        <div
                          key={record.id}
                          data-record-id={record.id}
                          className="bg-white rounded-lg border border-slate-200 p-4 shadow-sm hover:shadow-md transition-shadow"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="font-medium text-slate-800 text-base">{record.name}</div>
                              <div className="text-sm text-slate-500 mt-1">
                                {record.weight}g • {record.calories} kcal
                                {record.aiRecognition && (
                                  <span className="ml-2 badge badge-xs badge-primary">
                                    AI识别
                                  </span>
                                )}
                              </div>
                            </div>
                            <div className="flex gap-2 ml-3">
                              <button
                                onClick={() => handleEditRecord(record)}
                                className="btn btn-ghost btn-sm w-8 h-8 min-h-8 p-0"
                              >
                                ✏️
                              </button>
                              <button
                                onClick={() => handleDeleteRecord(record.id)}
                                className="btn btn-ghost btn-sm w-8 h-8 min-h-8 p-0 text-red-500 hover:text-red-700"
                              >
                                🗑️
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  if (!profile) {
    return (
      <div className="min-h-screen bg-base-200 flex items-center justify-center p-4">
        <div className="text-center">
          <div className="text-6xl mb-4">👤</div>
          <h2 className="text-xl font-bold mb-2">未找到用户档案</h2>
          <p className="text-slate-600 mb-4">请先完成个人档案设置</p>
          <button
            className="btn btn-primary"
            onClick={() => navigate('/setup')}
          >
            立即设置
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-base-200 pb-20">
      {/* 主要内容 - 极简布局 */}
      <div className="max-w-md mx-auto px-4 pt-4 pb-6">
        {activeView === 'list' && renderTimelineView()}
        {activeView === 'add-text' && renderTextInputView()}
        {activeView === 'add-image' && renderImageInputView()}
      </div>

      {/* 固定添加按钮 */}
      {activeView === 'list' && (
        <div className="fixed bottom-20 right-4 z-40">
          <div className="dropdown dropdown-top dropdown-end">
            <div tabIndex={0} role="button" className="btn btn-primary btn-circle btn-lg shadow-lg">
              <span className="text-xl">+</span>
            </div>
            <ul tabIndex={0} className="dropdown-content menu bg-white rounded-box z-[1] w-52 p-2 shadow-xl border border-slate-200 mb-2">
              <li>
                <button onClick={() => setActiveView('add-text')} className="flex items-center gap-3">
                  <span className="text-lg">📝</span>
                  <div>
                    <div className="font-medium">文字识别</div>
                    <div className="text-xs text-slate-500">描述食物内容</div>
                  </div>
                </button>
              </li>
              <li>
                <button onClick={() => setActiveView('add-image')} className="flex items-center gap-3">
                  <span className="text-lg">📷</span>
                  <div>
                    <div className="font-medium">拍照识别</div>
                    <div className="text-xs text-slate-500">AI识别食物</div>
                  </div>
                </button>
              </li>
            </ul>
          </div>
        </div>
      )}

      <BottomNavigation />

      {/* 编辑模态框 */}
      {showEditModal && editingRecord && (
        <div className="modal modal-open">
          <div className="modal-box">
            <h3 className="font-bold text-lg mb-4">编辑食物记录</h3>

            <div className="space-y-4">
              {/* 食物名称 */}
              <div>
                <label className="label">
                  <span className="label-text">食物名称</span>
                </label>
                <input
                  type="text"
                  value={editForm.name}
                  onChange={(e) => setEditForm({...editForm, name: e.target.value})}
                  className="input input-bordered w-full"
                />
              </div>

              {/* 重量 */}
              <div>
                <label className="label">
                  <span className="label-text">重量 (g)</span>
                </label>
                <input
                  type="number"
                  value={editForm.weight}
                  onChange={(e) => setEditForm({...editForm, weight: Number(e.target.value)})}
                  className="input input-bordered w-full"
                  min="1"
                />
              </div>

              {/* 卡路里 */}
              <div>
                <label className="label">
                  <span className="label-text">卡路里 (kcal)</span>
                </label>
                <input
                  type="number"
                  value={editForm.calories}
                  onChange={(e) => setEditForm({...editForm, calories: Number(e.target.value)})}
                  className="input input-bordered w-full"
                  min="1"
                />
              </div>

              {/* 餐次 */}
              <div>
                <label className="label">
                  <span className="label-text">餐次</span>
                </label>
                <select
                  value={editForm.mealType}
                  onChange={(e) => setEditForm({...editForm, mealType: e.target.value as MealType})}
                  className="select select-bordered w-full"
                >
                  {(Object.keys(MEAL_CONFIG) as MealType[]).map((mealType) => (
                    <option key={mealType} value={mealType}>
                      {MEAL_CONFIG[mealType].label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="modal-action">
              <button
                onClick={() => setShowEditModal(false)}
                className="btn btn-ghost"
              >
                取消
              </button>
              <button
                onClick={handleSaveEdit}
                className="btn btn-primary"
                disabled={!editForm.name || editForm.weight <= 0 || editForm.calories <= 0}
              >
                保存
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FoodRecordPage;
