import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useNutritionStore } from '@/domains/nutrition/stores/nutritionStore';
import { useUserStore } from '@/domains/user/stores/userStore';
import { geminiService } from '@/infrastructure/ai/geminiService';
import { FoodRecord as FoodRecordType, MealType } from '@/shared/types';
import BottomNavigation from '@/shared/components/navigation/BottomNavigationNew';
import { formatDate } from '@/shared/utils';
import { getCurrentSnackTimeInfo, calculateSnackCalories } from '@/shared/utils/snackTimeUtils';
// import anime from 'animejs/lib/anime.es.js';

// 餐次配置
const MEAL_CONFIG = {
  breakfast: { 
    label: '早餐', 
    icon: '🌅', 
    timeRange: '6:00-10:00',
    color: 'text-orange-600 bg-orange-50 border-orange-200'
  },
  lunch: { 
    label: '午餐', 
    icon: '☀️', 
    timeRange: '11:00-14:00',
    color: 'text-green-600 bg-green-50 border-green-200'
  },
  dinner: { 
    label: '晚餐', 
    icon: '🌙', 
    timeRange: '17:00-21:00',
    color: 'text-blue-600 bg-blue-50 border-blue-200'
  },
  snack: {
    label: '加餐',
    icon: '🍎',
    timeRange: '随时',
    color: 'text-emerald-600 bg-emerald-50 border-emerald-200'
  }
};

// 获取当前餐次
const getCurrentMealType = (): MealType => {
  const hour = new Date().getHours();
  if (hour >= 6 && hour < 11) return 'breakfast';
  if (hour >= 11 && hour < 15) return 'lunch';
  if (hour >= 17 && hour < 22) return 'dinner';
  return 'snack';
};

// 获取动态的加餐配置
const getSnackConfig = () => {
  const snackInfo = getCurrentSnackTimeInfo();
  const currentPeriod = snackInfo.currentPeriod || snackInfo.nextPeriod;

  return {
    label: currentPeriod?.name || '加餐',
    icon: currentPeriod?.emoji || '🍎',
    timeRange: currentPeriod?.timeRange || '随时',
    color: 'text-emerald-600 bg-emerald-50 border-emerald-200',
    description: snackInfo.isSnackTime
      ? `现在是${currentPeriod?.name}时间`
      : `${currentPeriod?.name}将在${snackInfo.timeUntilNext}开始`
  };
};

const FoodRecordPage: React.FC = () => {
  const navigate = useNavigate();
  const { profile } = useUserStore();
  const {
    getDailyFoodRecords,
    addDetailedFoodRecord,
    updateFoodRecord,
    deleteFoodRecord
  } = useNutritionStore();

  // 状态管理
  const [currentDate, setCurrentDate] = useState(new Date());

  // 编辑状态
  const [editingRecord, setEditingRecord] = useState<FoodRecordType | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editForm, setEditForm] = useState({
    name: '',
    weight: 0,
    calories: 0,
    mealType: 'breakfast' as MealType
  });

  // 获取当日食物记录
  const dailyRecords = getDailyFoodRecords(currentDate);

  // 页面动画初始化 (暂时禁用)
  useEffect(() => {
    // TODO: 修复anime.js导入问题后重新启用动画
    // if (activeView === 'list') {
    //   const timelineItems = document.querySelectorAll('.food-timeline-item');
    //   if (timelineItems.length > 0) {
    //     anime({
    //       targets: '.food-timeline-item',
    //       translateY: [20, 0],
    //       opacity: [0, 1],
    //       delay: anime.stagger(100),
    //       duration: 600,
    //       easing: 'easeOutQuad'
    //     });
    //   }
    // }
  }, [dailyRecords]);



  // 删除食物记录
  const handleDeleteRecord = (recordId: string) => {
    if (window.confirm('确定要删除这条食物记录吗？')) {
      // 直接删除数据 (动画暂时禁用)
      deleteFoodRecord(currentDate, recordId);

      // TODO: 修复anime.js导入问题后重新启用删除动画
      // const targetElement = document.querySelector(`[data-record-id="${recordId}"]`);
      // if (targetElement) {
      //   anime({
      //     targets: `[data-record-id="${recordId}"]`,
      //     translateX: [-300],
      //     opacity: [0],
      //     duration: 400,
      //     easing: 'easeInQuad',
      //     complete: () => {
      //       deleteFoodRecord(currentDate, recordId);
      //     }
      //   });
      // }
    }
  };

  // 编辑食物记录
  const handleEditRecord = (record: FoodRecordType) => {
    setEditingRecord(record);
    setEditForm({
      name: record.name,
      weight: record.weight,
      calories: record.calories,
      mealType: record.mealType
    });
    setShowEditModal(true);
  };

  // 保存编辑
  const handleSaveEdit = () => {
    if (!editingRecord) return;

    updateFoodRecord(currentDate, editingRecord.id, {
      name: editForm.name,
      weight: editForm.weight,
      calories: editForm.calories,
      mealType: editForm.mealType
    });

    setShowEditModal(false);
    setEditingRecord(null);
  };





  // 渲染时间轴视图
  const renderTimelineView = () => {
    const mealTypes: MealType[] = ['breakfast', 'lunch', 'dinner', 'snack'];
    
    return (
      <div className="space-y-6">
        {/* 简化日期选择器 */}
        <div className="flex items-center justify-center bg-white rounded-xl p-3 shadow-sm border border-slate-200 mb-1">
          <div className="flex items-center gap-3">
            <button
              onClick={() => setCurrentDate(new Date(currentDate.getTime() - 24 * 60 * 60 * 1000))}
              className="btn btn-ghost btn-sm w-8 h-8 min-h-8 p-0"
            >
              ←
            </button>
            <div className="text-center min-w-[100px]">
              <div className="font-bold text-slate-800 text-base">
                {formatDate(currentDate, 'MM月dd日')}
              </div>
              <div className="text-xs text-slate-500 -mt-1">
                {formatDate(currentDate, 'EEEE')}
              </div>
            </div>
            <button
              onClick={() => setCurrentDate(new Date(currentDate.getTime() + 24 * 60 * 60 * 1000))}
              className="btn btn-ghost btn-sm w-8 h-8 min-h-8 p-0"
              disabled={formatDate(currentDate, 'yyyy-MM-dd') >= formatDate(new Date(), 'yyyy-MM-dd')}
            >
              →
            </button>
          </div>
        </div>

        {/* 左对齐时间轴 */}
        <div className="space-y-4">
          {mealTypes.map((mealType, index) => {
            const mealRecords = dailyRecords?.mealRecords[mealType] || [];
            const mealConfig = mealType === 'snack' ? getSnackConfig() : MEAL_CONFIG[mealType];
            const totalCalories = mealRecords.reduce((sum, record) => sum + record.calories, 0);

            return (
              <div key={mealType} className={`food-timeline-item ${index === 0 ? 'mt-4' : ''}`}>
                {/* 餐次标题行 */}
                <div className="flex items-center gap-3 mb-3">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${mealConfig.color} border-2 flex-shrink-0`}>
                    <span className="text-lg">{mealConfig.icon}</span>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <h3 className="font-bold text-slate-800 text-lg">{mealConfig.label}</h3>
                        <div className={`badge ${mealConfig.color} font-medium text-xs`}>
                          {mealConfig.timeRange}
                        </div>
                      </div>
                      <div className="text-sm text-slate-500 font-medium">
                        {totalCalories} kcal
                        {mealType === 'snack' && profile && (
                          <div className="text-xs text-emerald-600 mt-1">
                            / {calculateSnackCalories(profile.dailyCalorieLimit).totalSnackCalories} kcal
                          </div>
                        )}
                      </div>
                    </div>
                    {mealType === 'snack' && mealConfig.description && (
                      <div className="text-xs text-emerald-600 mt-1">
                        💡 {mealConfig.description}
                      </div>
                    )}
                  </div>
                </div>

                {/* 食物记录卡片区域 */}
                <div className="ml-13 mb-6">
                  {mealRecords.length === 0 ? (
                    <div className="bg-white rounded-lg border border-slate-200 p-6 text-center text-slate-400">
                      <div className="text-3xl mb-2">🍽️</div>
                      <div className="text-sm">暂无记录</div>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {mealRecords.map((record) => (
                        <div
                          key={record.id}
                          data-record-id={record.id}
                          className="bg-white rounded-lg border border-slate-200 p-4 shadow-sm hover:shadow-md transition-shadow"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="font-medium text-slate-800 text-base">{record.name}</div>
                              <div className="text-sm text-slate-500 mt-1">
                                {record.weight}g • {record.calories} kcal
                                {record.aiRecognition && (
                                  <span className="ml-2 badge badge-xs badge-primary">
                                    {record.aiRecognition.method === 'image' ? '图片识别' : '对话识别'}
                                  </span>
                                )}
                              </div>
                            </div>
                            <div className="flex gap-2 ml-3">
                              <button
                                onClick={() => handleEditRecord(record)}
                                className="btn btn-ghost btn-sm w-8 h-8 min-h-8 p-0"
                              >
                                ✏️
                              </button>
                              <button
                                onClick={() => handleDeleteRecord(record.id)}
                                className="btn btn-ghost btn-sm w-8 h-8 min-h-8 p-0 text-red-500 hover:text-red-700"
                              >
                                🗑️
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  if (!profile) {
    return (
      <div className="min-h-screen bg-base-200 flex items-center justify-center p-4">
        <div className="text-center">
          <div className="text-6xl mb-4">👤</div>
          <h2 className="text-xl font-bold mb-2">未找到用户档案</h2>
          <p className="text-slate-600 mb-4">请先完成个人档案设置</p>
          <button
            className="btn btn-primary"
            onClick={() => navigate('/setup')}
          >
            立即设置
          </button>
        </div>
      </div>
    );
  }

  // 处理识别完成
  const handleRecognitionComplete = (result: any) => {
    console.log('识别完成:', result);
    // 这里可以添加将结果保存到状态的逻辑
  };

  return (
    <div className="min-h-screen bg-base-200 pb-20">
      {/* 主要内容 - 只显示时间线视图 */}
      <div className="max-w-md mx-auto px-4 pt-4 pb-6">
        {renderTimelineView()}
      </div>



      <BottomNavigation
        showAddButton={true}
        onRecognitionComplete={handleRecognitionComplete}
      />

      {/* 编辑模态框 */}
      {showEditModal && editingRecord && (
        <div className="modal modal-open">
          <div className="modal-box">
            <h3 className="font-bold text-lg mb-4">编辑食物记录</h3>

            <div className="space-y-4">
              {/* 食物名称 */}
              <div>
                <label className="label">
                  <span className="label-text">食物名称</span>
                </label>
                <input
                  type="text"
                  value={editForm.name}
                  onChange={(e) => setEditForm({...editForm, name: e.target.value})}
                  className="input input-bordered w-full"
                />
              </div>

              {/* 重量 */}
              <div>
                <label className="label">
                  <span className="label-text">重量 (g)</span>
                </label>
                <input
                  type="number"
                  value={editForm.weight}
                  onChange={(e) => setEditForm({...editForm, weight: Number(e.target.value)})}
                  className="input input-bordered w-full"
                  min="1"
                />
              </div>

              {/* 卡路里 */}
              <div>
                <label className="label">
                  <span className="label-text">卡路里 (kcal)</span>
                </label>
                <input
                  type="number"
                  value={editForm.calories}
                  onChange={(e) => setEditForm({...editForm, calories: Number(e.target.value)})}
                  className="input input-bordered w-full"
                  min="1"
                />
              </div>

              {/* 餐次 */}
              <div>
                <label className="label">
                  <span className="label-text">餐次</span>
                </label>
                <select
                  value={editForm.mealType}
                  onChange={(e) => setEditForm({...editForm, mealType: e.target.value as MealType})}
                  className="select select-bordered w-full"
                >
                  {(Object.keys(MEAL_CONFIG) as MealType[]).map((mealType) => (
                    <option key={mealType} value={mealType}>
                      {MEAL_CONFIG[mealType].label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="modal-action">
              <button
                onClick={() => setShowEditModal(false)}
                className="btn btn-ghost"
              >
                取消
              </button>
              <button
                onClick={handleSaveEdit}
                className="btn btn-primary"
                disabled={!editForm.name || editForm.weight <= 0 || editForm.calories <= 0}
              >
                保存
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FoodRecordPage;
