import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useUserStore } from '@/domains/user/stores/userStore';
import { useNutritionStore } from '@/domains/nutrition/stores/nutritionStore';
import { indexedDBService, StorageStatus } from '@/infrastructure/storage/indexedDBService';
import { useAIModelStore } from '@/domains/ai/stores/aiModelStore';
import BottomNavigation from '@/shared/components/navigation/BottomNavigationNew';
import AIModelManagementModal from '@/shared/components/modals/AIModelManagementModal';

const ProfilePage: React.FC = () => {
  const navigate = useNavigate();
  const { profile, clearProfile } = useUserStore();
  const { dailySummaries, clearAllData } = useNutritionStore();
  const { models, getActiveModel } = useAIModelStore();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [storageStatus, setStorageStatus] = useState<StorageStatus | null>(null);
  const [showStorageWarning, setShowStorageWarning] = useState(false);
  const [showAIModelModal, setShowAIModelModal] = useState(false);
  const [showProfileEditModal, setShowProfileEditModal] = useState(false);

  // 获取存储状态
  const updateStorageStatus = async () => {
    try {
      const status = await indexedDBService.getStorageStatus();
      setStorageStatus(status);

      // 检查是否需要显示存储警告
      if (status.quota.percentage >= 85) {
        setShowStorageWarning(true);
      }
    } catch (error) {
      console.error('获取存储状态失败:', error);
    }
  };

  // 格式化存储大小显示
  const formatStorageSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 检查持久化存储权限
  const handleRequestPersistentStorage = async () => {
    try {
      const granted = await indexedDBService.requestPersistentStorage();
      // 更新存储状态
      await updateStorageStatus();

      if (granted) {
        alert('✅ 持久化存储已启用！\n\n您的营养追踪数据受到保护，不会在浏览器清理时丢失。');
      } else {
        alert('ℹ️ 持久化存储状态\n\n现代浏览器（如Chrome）通常会在适当时机自动授予持久化存储权限。\n\n您的数据目前是安全的，无需担心。如果您经常使用此应用，浏览器会自动提升存储优先级。\n\n建议：\n• 将应用添加到主屏幕（PWA安装）\n• 定期使用应用\n• 可选择性导出数据作为备份');
      }
    } catch (error) {
      console.error('检查持久化存储权限失败:', error);
      alert('ℹ️ 无法检查存储状态\n\n这通常不影响应用正常使用。现代浏览器会自动管理存储权限。\n\n建议定期导出数据作为备份。');
    }
  };

  // 数据导出功能
  const handleExportData = () => {
    try {
      // 收集所有用户数据
      const exportData = {
        version: '1.0',
        timestamp: new Date().toISOString(),
        profile: profile,
        nutritionRecords: Object.values(dailySummaries),
        // 可以添加其他数据
        settings: {
          // 应用设置和偏好
        }
      };

      // 生成JSON文件
      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });

      // 创建下载链接
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `kcal-tracker-data-${new Date().toISOString().split('T')[0]}.json`;

      // 触发下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      alert('数据导出成功！');
    } catch (error) {
      console.error('数据导出失败:', error);
      alert('数据导出失败，请重试。');
    }
  };

  // 数据导入功能
  const handleImportData = () => {
    fileInputRef.current?.click();
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const text = await file.text();
      const importData = JSON.parse(text);

      // 验证数据格式
      if (!importData.version || !importData.profile || !importData.nutritionRecords) {
        throw new Error('数据格式不正确');
      }

      // 确认导入
      const confirmed = confirm('导入数据将替换当前所有数据，是否继续？');
      if (!confirmed) return;

      // 导入数据
      // TODO: 实现profile导入功能
      // setProfile(importData.profile);
      // TODO: 实现nutrition records导入功能
      // importRecords(importData.nutritionRecords);

      alert('数据导入成功！');

      // 重新加载页面以反映更改
      window.location.reload();
    } catch (error) {
      console.error('数据导入失败:', error);
      alert('数据导入失败，请检查文件格式。');
    }

    // 清空文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Anime.js v4兼容动画系统 - 确保不影响底部导航栏
  useEffect(() => {
    // 获取存储状态
    updateStorageStatus();

    // 导入Anime.js动画函数
    import('@/utils/animations').then(({ createPageLoadAnimation }) => {
      // 页面加载动画 - 使用opacity避免transform影响fixed定位
      const cardElements = [
        '.profile-card',
        '.action-button',
        '.settings-card',
        '.stats-card'
      ];

      // 创建页面加载动画
      createPageLoadAnimation(cardElements);

      // 添加数字递增动画
      setTimeout(async () => {
        const { createCountUpAnimation } = await import('@/utils/animations');

        // 数字动画
        const numberElements = document.querySelectorAll('.profile-number');
        numberElements.forEach((element: Element) => {
          const htmlElement = element as HTMLElement;
          const targetValue = parseFloat(htmlElement.textContent || '0');
          if (targetValue > 0) {
            createCountUpAnimation(htmlElement, targetValue, 1200);
          }
        });
      }, 500);
    });
  }, []);

  // Anime.js v4按钮点击动画 - 兼容底部导航栏
  const handleButtonClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    import('@/utils/animations').then(({ createButtonAnimation }) => {
      createButtonAnimation(e.currentTarget as HTMLElement);
    });
  };

  const handleCardHover = (e: React.MouseEvent<HTMLDivElement>) => {
    // Anime.js v4卡片hover动画 - 兼容底部导航栏
    const element = e.currentTarget as HTMLElement;
    element.classList.add('card-hover-target');
    import('@/utils/animations').then(({ createCardHoverAnimation }) => {
      createCardHoverAnimation('.card-hover-target', true);
    });
  };

  const handleCardLeave = (e: React.MouseEvent<HTMLDivElement>) => {
    // Anime.js v4卡片leave动画
    const element = e.currentTarget as HTMLElement;
    element.classList.add('card-leave-target');
    import('@/utils/animations').then(({ createCardHoverAnimation }) => {
      createCardHoverAnimation('.card-leave-target', false);
    });
    setTimeout(() => {
      element.classList.remove('card-hover-target', 'card-leave-target');
    }, 350);
  };



  if (!profile) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center p-4">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl mx-auto mb-4 flex items-center justify-center">
            <span className="text-2xl">👤</span>
          </div>
          <h2 className="text-xl font-bold text-slate-800 mb-2">
            未找到用户档案
          </h2>
          <p className="text-slate-600 mb-4">
            请先完成个人档案设置
          </p>
          <button
            className="btn btn-primary btn-lg"
            onClick={() => navigate('/setup')}
          >
            立即设置
          </button>
        </div>
      </div>
    );
  }

  const weightLoss = profile.weight - profile.targetWeight;
  const weeklyLoss = (weightLoss / profile.targetDays) * 7;
  const bmi = profile.weight / Math.pow(profile.height / 100, 2);

  return (
    <div className="relative">
      {/* 主要内容容器 - 完全避免transform，确保不影响fixed定位 */}
      <div
        className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50"
        style={{
          // 强制确保没有任何transform属性
          transform: 'none',
          // 避免创建层叠上下文
          isolation: 'auto',
          // 确保不影响fixed定位的子元素
          position: 'relative',
          zIndex: 'auto'
        }}
      >
        {/* 移动端优先的容器设计 */}
        <div className="w-full max-w-none sm:max-w-lg md:max-w-2xl lg:max-w-4xl xl:max-w-6xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
          <div className="space-y-4 sm:space-y-6">
            {/* 页面标题 */}
            <div className="text-center mb-8">
              <h1 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-2">
                我的
              </h1>
              <p className="text-slate-600">个人信息与设置</p>
            </div>

            {/* 个人档案信息卡片 - 从Dashboard迁移 */}
            <div
              className="profile-card relative overflow-hidden bg-gradient-to-br from-sky-50 via-blue-50 to-indigo-50 rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-sky-200/50 shadow-xl"
              onMouseEnter={handleCardHover}
              onMouseLeave={handleCardLeave}
            >
              {/* 背景装饰 */}
              <div className="absolute top-0 right-0 w-36 h-36 bg-gradient-to-br from-sky-200/20 to-blue-200/20 rounded-full -translate-y-18 translate-x-18"></div>
              <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-indigo-200/20 to-sky-200/20 rounded-full translate-y-16 -translate-x-16"></div>

              <div className="relative z-10">
                {/* 标题区域 */}
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-10 h-10 bg-gradient-to-r from-sky-500 to-blue-500 rounded-xl flex items-center justify-center shadow-lg">
                    <span className="text-xl">👤</span>
                  </div>
                  <h3 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-sky-600 to-blue-600 bg-clip-text text-transparent">
                    个人档案
                  </h3>
                </div>

                {/* 个人信息网格 */}
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center shadow-md border border-white/50">
                    <div className="profile-number text-2xl font-bold text-sky-600 mb-1">
                      {Math.round(profile.weight)}
                    </div>
                    <div className="text-xs text-slate-600">当前体重</div>
                    <div className="text-xs text-sky-500">kg</div>
                  </div>
                  <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center shadow-md border border-white/50">
                    <div className="profile-number text-2xl font-bold text-blue-600 mb-1">
                      {Math.round(profile.targetWeight)}
                    </div>
                    <div className="text-xs text-slate-600">目标体重</div>
                    <div className="text-xs text-blue-500">kg</div>
                  </div>
                  <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center shadow-md border border-white/50">
                    <div className="profile-number text-2xl font-bold text-indigo-600 mb-1">
                      {profile.targetDays}
                    </div>
                    <div className="text-xs text-slate-600">目标天数</div>
                    <div className="text-xs text-indigo-500">天</div>
                  </div>
                  <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center shadow-md border border-white/50">
                    <div className="profile-number text-2xl font-bold text-cyan-600 mb-1">
                      {Math.round(weightLoss * 10) / 10}
                    </div>
                    <div className="text-xs text-slate-600">需减重量</div>
                    <div className="text-xs text-cyan-500">kg</div>
                  </div>
                </div>


              </div>
            </div>

            {/* 数据统计卡片 */}
            <div
              className="stats-card relative overflow-hidden bg-gradient-to-br from-emerald-50 via-teal-50 to-cyan-50 rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-emerald-200/50 shadow-xl"
              onMouseEnter={handleCardHover}
              onMouseLeave={handleCardLeave}
            >
              {/* 背景装饰 */}
              <div className="absolute top-0 left-0 w-40 h-40 bg-gradient-to-br from-emerald-200/20 to-teal-200/20 rounded-full -translate-y-20 -translate-x-20"></div>
              <div className="absolute bottom-0 right-0 w-28 h-28 bg-gradient-to-tl from-cyan-200/20 to-emerald-200/20 rounded-full translate-y-14 translate-x-14"></div>

              <div className="relative z-10">
                {/* 标题区域 */}
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-10 h-10 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl flex items-center justify-center shadow-lg">
                    <span className="text-xl">📊</span>
                  </div>
                  <h3 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                    健康数据
                  </h3>
                </div>

                {/* 健康数据网格 */}
                <div className="grid grid-cols-2 gap-4">
                  {/* BMI */}
                  <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center shadow-md border border-white/50">
                    <div className="profile-number text-2xl font-bold text-emerald-600 mb-1">
                      {Math.round(bmi * 10) / 10}
                    </div>
                    <div className="text-xs text-slate-600">BMI指数</div>
                    <div className="text-xs text-emerald-500">
                      {bmi < 18.5 ? '偏瘦' : bmi < 24 ? '正常' : bmi < 28 ? '偏胖' : '肥胖'}
                    </div>
                  </div>

                  {/* 身高 */}
                  <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center shadow-md border border-white/50">
                    <div className="profile-number text-2xl font-bold text-teal-600 mb-1">
                      {profile.height}
                    </div>
                    <div className="text-xs text-slate-600">身高</div>
                    <div className="text-xs text-teal-500">cm</div>
                  </div>

                  {/* 每周减重 */}
                  <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center shadow-md border border-white/50">
                    <div className="profile-number text-2xl font-bold text-cyan-600 mb-1">
                      {Math.round(weeklyLoss * 10) / 10}
                    </div>
                    <div className="text-xs text-slate-600">每周减重</div>
                    <div className="text-xs text-cyan-500">kg/周</div>
                  </div>

                  {/* 年龄 */}
                  <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center shadow-md border border-white/50">
                    <div className="profile-number text-2xl font-bold text-blue-600 mb-1">
                      {profile.age}
                    </div>
                    <div className="text-xs text-slate-600">年龄</div>
                    <div className="text-xs text-blue-500">岁</div>
                  </div>
                </div>
              </div>
            </div>

            {/* 存储状态卡片 */}
            {storageStatus && (
              <div
                className="storage-card relative overflow-hidden bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50 rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-orange-200/50 shadow-xl"
                onMouseEnter={handleCardHover}
                onMouseLeave={handleCardLeave}
              >
                {/* 背景装饰 */}
                <div className="absolute top-0 left-0 w-40 h-40 bg-gradient-to-br from-orange-200/20 to-amber-200/20 rounded-full -translate-y-20 -translate-x-20"></div>
                <div className="absolute bottom-0 right-0 w-28 h-28 bg-gradient-to-tl from-yellow-200/20 to-orange-200/20 rounded-full translate-y-14 translate-x-14"></div>

                <div className="relative z-10">
                  {/* 标题区域 */}
                  <div className="flex items-center gap-3 mb-6">
                    <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-amber-500 rounded-xl flex items-center justify-center shadow-lg">
                      <span className="text-xl">💾</span>
                    </div>
                    <h3 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-orange-600 to-amber-600 bg-clip-text text-transparent">
                      存储状态
                    </h3>
                  </div>

                  {/* 存储信息网格 */}
                  <div className="space-y-4">
                    {/* 存储使用率进度条 */}
                    <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/50">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-slate-600">存储使用率</span>
                        <span className={`text-sm font-bold ${storageStatus.quota.percentage >= 85 ? 'text-red-600' : storageStatus.quota.percentage >= 70 ? 'text-amber-600' : 'text-green-600'}`}>
                          {storageStatus.quota.percentage}%
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
                        <div
                          className={`h-3 rounded-full transition-all duration-300 ${
                            storageStatus.quota.percentage >= 85 ? 'bg-gradient-to-r from-red-400 to-red-600' :
                            storageStatus.quota.percentage >= 70 ? 'bg-gradient-to-r from-amber-400 to-amber-600' :
                            'bg-gradient-to-r from-green-400 to-green-600'
                          }`}
                          style={{ width: `${Math.min(storageStatus.quota.percentage, 100)}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between text-xs text-slate-500">
                        <span>已用: {formatStorageSize(storageStatus.quota.used)}</span>
                        <span>总计: {formatStorageSize(storageStatus.quota.total)}</span>
                      </div>


                    </div>



                    {/* 存储警告 */}
                    {showStorageWarning && (
                      <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-red-600">⚠️</span>
                          <span className="text-sm font-medium text-red-800">存储空间警告</span>
                        </div>
                        <p className="text-xs text-red-700 mb-3">
                          存储空间使用率已达到85%，建议导出数据以释放空间。
                        </p>
                        <div className="flex gap-2">
                          <button
                            onClick={handleExportData}
                            className="flex-1 bg-red-600 text-white text-xs py-2 px-3 rounded-lg hover:bg-red-700 transition-colors"
                          >
                            立即导出
                          </button>
                          <button
                            onClick={() => setShowStorageWarning(false)}
                            className="flex-1 bg-gray-300 text-gray-700 text-xs py-2 px-3 rounded-lg hover:bg-gray-400 transition-colors"
                          >
                            稍后处理
                          </button>
                        </div>
                      </div>
                    )}

                    {/* 存储模式状态 */}
                    <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/50">
                      <div className="flex items-center gap-3">
                        <div className={`text-lg ${storageStatus.isPersistent ? 'text-green-600' : 'text-amber-600'}`}>
                          {storageStatus.isPersistent ? '✅' : '📱'}
                        </div>
                        <div className="flex-1">
                          <div className="text-sm font-medium text-slate-800">
                            当前存储模式：{storageStatus.isPersistent ? '持久化存储' : '临时存储'}
                          </div>
                          <div className="text-xs text-slate-600">
                            {storageStatus.isPersistent
                              ? '数据受到保护，不会在浏览器清理时丢失'
                              : '浏览器会根据使用情况自动管理存储优先级'
                            }
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 设置选项卡片 */}
            <div
              className="settings-card relative overflow-hidden bg-gradient-to-br from-violet-50 via-purple-50 to-indigo-50 rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-violet-200/50 shadow-xl"
              onMouseEnter={handleCardHover}
              onMouseLeave={handleCardLeave}
            >
              {/* 背景装饰 */}
              <div className="absolute top-0 right-0 w-36 h-36 bg-gradient-to-br from-violet-200/20 to-purple-200/20 rounded-full -translate-y-18 translate-x-18"></div>
              <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-indigo-200/20 to-violet-200/20 rounded-full translate-y-16 -translate-x-16"></div>

              <div className="relative z-10">
                {/* 标题区域 */}
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-10 h-10 bg-gradient-to-r from-violet-500 to-purple-500 rounded-xl flex items-center justify-center shadow-lg">
                    <span className="text-xl">⚙️</span>
                  </div>
                  <h3 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent">
                    设置选项
                  </h3>
                </div>

                {/* 设置选项列表 */}
                <div className="space-y-3">
                  <button
                    className="action-button w-full bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/50 hover:bg-white/90 transition-all duration-200"
                    onClick={(e) => { handleButtonClick(e); setShowProfileEditModal(true); }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
                          <span className="text-sm">✏️</span>
                        </div>
                        <div className="text-left">
                          <div className="font-bold text-sm text-slate-800">档案管理</div>
                          <div className="text-xs text-slate-600">编辑或重新设置档案</div>
                        </div>
                      </div>
                      <span className="text-slate-400">›</span>
                    </div>
                  </button>

                  <button
                    className="action-button w-full bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/50 hover:bg-white/90 transition-all duration-200"
                    onClick={(e) => { handleButtonClick(e); alert('通知设置功能即将推出'); }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                          <span className="text-sm">🔔</span>
                        </div>
                        <div className="text-left">
                          <div className="font-bold text-sm text-slate-800">通知设置</div>
                          <div className="text-xs text-slate-600">管理提醒通知</div>
                        </div>
                      </div>
                      <span className="text-slate-400">›</span>
                    </div>
                  </button>

                  <button
                    className="action-button w-full bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/50 hover:bg-white/90 transition-all duration-200"
                    onClick={(e) => { handleButtonClick(e); handleExportData(); }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                          <span className="text-sm">📤</span>
                        </div>
                        <div className="text-left">
                          <div className="font-bold text-sm text-slate-800">数据导出</div>
                          <div className="text-xs text-slate-600">导出健康数据</div>
                        </div>
                      </div>
                      <span className="text-slate-400">›</span>
                    </div>
                  </button>

                  <button
                    className="action-button w-full bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/50 hover:bg-white/90 transition-all duration-200"
                    onClick={(e) => { handleButtonClick(e); handleImportData(); }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-teal-500 to-cyan-500 rounded-lg flex items-center justify-center">
                          <span className="text-sm">📥</span>
                        </div>
                        <div className="text-left">
                          <div className="font-bold text-sm text-slate-800">数据导入</div>
                          <div className="text-xs text-slate-600">导入健康数据</div>
                        </div>
                      </div>
                      <span className="text-slate-400">›</span>
                    </div>
                  </button>

                  <button
                    className="action-button w-full bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/50 hover:bg-white/90 transition-all duration-200"
                    onClick={(e) => { handleButtonClick(e); setShowAIModelModal(true); }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                          <span className="text-sm">🤖</span>
                        </div>
                        <div className="text-left">
                          <div className="font-bold text-sm text-slate-800">AI模型管理</div>
                          <div className="text-xs text-slate-600">
                            {models.length > 0 ? `已配置 ${models.length} 个模型` : '配置AI模型'}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {getActiveModel() && (
                          <span className="text-xs bg-emerald-100 text-emerald-800 px-2 py-1 rounded-full">
                            {getActiveModel()?.supportsVision ? '支持图像' : '仅文本'}
                          </span>
                        )}
                        <span className="text-slate-400">›</span>
                      </div>
                    </div>
                  </button>

                  <button
                    className="action-button w-full bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/50 hover:bg-white/90 transition-all duration-200"
                    onClick={(e) => { handleButtonClick(e); alert('关于我们功能即将推出'); }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                          <span className="text-sm">ℹ️</span>
                        </div>
                        <div className="text-left">
                          <div className="font-bold text-sm text-slate-800">关于我们</div>
                          <div className="text-xs text-slate-600">应用信息与帮助</div>
                        </div>
                      </div>
                      <span className="text-slate-400">›</span>
                    </div>
                  </button>
                </div>
              </div>
            </div>

            {/* 底部留白，避免内容被底部导航栏遮挡 - 适配全面屏安全区域 */}
            <div className="h-24 pb-safe"></div>
          </div>
        </div>
      </div>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".json"
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* 底部固定导航栏 - 移到根级别避免transform层叠上下文影响 */}
      <BottomNavigation
        showAddButton={true}
        onRecognitionComplete={(result: any) => {
          console.log('Profile收到识别结果:', result);
        }}
      />

      {/* AI模型管理Modal */}
      <AIModelManagementModal
        isOpen={showAIModelModal}
        onClose={() => setShowAIModelModal(false)}
      />

      {/* 档案编辑模态框 */}
      {showProfileEditModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md mx-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-slate-800">档案管理</h3>
                <button
                  onClick={() => setShowProfileEditModal(false)}
                  className="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
                >
                  <span className="text-gray-600">✕</span>
                </button>
              </div>

              <div className="space-y-3">
                <button
                  onClick={() => {
                    setShowProfileEditModal(false);
                    navigate('/setup');
                  }}
                  className="w-full bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-xl p-4 hover:from-blue-600 hover:to-indigo-600 transition-all duration-200 shadow-lg"
                >
                  <div className="flex items-center gap-3">
                    <span className="text-xl">✏️</span>
                    <div className="text-left">
                      <div className="font-bold">编辑档案</div>
                      <div className="text-sm opacity-90">修改个人信息和目标</div>
                    </div>
                  </div>
                </button>

                <button
                  onClick={() => {
                    if (confirm('确定要重新设置档案吗？这将清除所有个人信息，需要重新填写。')) {
                      clearProfile();
                      setShowProfileEditModal(false);
                      navigate('/setup');
                    }
                  }}
                  className="w-full bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-xl p-4 hover:from-orange-600 hover:to-red-600 transition-all duration-200 shadow-lg"
                >
                  <div className="flex items-center gap-3">
                    <span className="text-xl">🔄</span>
                    <div className="text-left">
                      <div className="font-bold">重新设置档案</div>
                      <div className="text-sm opacity-90">清除所有信息，重新开始</div>
                    </div>
                  </div>
                </button>
              </div>

              <div className="mt-6 pt-4 border-t border-gray-200">
                <button
                  onClick={() => setShowProfileEditModal(false)}
                  className="w-full bg-gray-100 text-gray-700 rounded-xl p-3 hover:bg-gray-200 transition-colors"
                >
                  取消
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfilePage;
