/**
 * 加餐时间段管理工具
 */

export interface SnackPeriod {
  id: 'morning' | 'afternoon' | 'evening';
  name: string;
  emoji: string;
  timeRange: string;
  startHour: number;
  endHour: number;
  description: string;
}

export interface SnackTimeInfo {
  currentPeriod: SnackPeriod | null;
  nextPeriod: SnackPeriod | null;
  isSnackTime: boolean;
  timeUntilNext: string;
}

// 定义加餐时间段
export const SNACK_PERIODS: SnackPeriod[] = [
  {
    id: 'morning',
    name: '上午加餐',
    emoji: '🥐',
    timeRange: '10:00-11:00',
    startHour: 10,
    endHour: 11,
    description: '早餐后补充'
  },
  {
    id: 'afternoon',
    name: '下午加餐',
    emoji: '🍎',
    timeRange: '14:00-17:00',
    startHour: 14,
    endHour: 17,
    description: '午后能量'
  },
  {
    id: 'evening',
    name: '晚间加餐',
    emoji: '🥛',
    timeRange: '21:00-24:00',
    startHour: 21,
    endHour: 24,
    description: '睡前小食'
  }
];

/**
 * 获取当前时间的加餐信息
 */
export function getCurrentSnackTimeInfo(currentTime: Date = new Date()): SnackTimeInfo {
  const currentHour = currentTime.getHours();
  const currentMinute = currentTime.getMinutes();
  const currentTimeInMinutes = currentHour * 60 + currentMinute;

  // 查找当前时间段（精确到分钟）
  const currentPeriod = SNACK_PERIODS.find(period => {
    const startMinutes = period.startHour * 60;
    // 处理跨越24:00的情况（如21:00-24:00）
    const endMinutes = period.endHour === 24 ? 24 * 60 : period.endHour * 60;

    console.log(`检查加餐时间段: ${period.name} (${period.startHour}:00-${period.endHour}:00), 当前时间分钟: ${currentTimeInMinutes}, 范围: ${startMinutes}-${endMinutes}`);

    return currentTimeInMinutes >= startMinutes && currentTimeInMinutes < endMinutes;
  });

  // 查找下一个时间段
  let nextPeriod: SnackPeriod | null = null;

  if (currentPeriod) {
    // 如果在加餐时间段内，下一个时间段是下一个加餐时间
    const currentIndex = SNACK_PERIODS.findIndex(p => p.id === currentPeriod.id);
    nextPeriod = SNACK_PERIODS[(currentIndex + 1) % SNACK_PERIODS.length];
  } else {
    // 如果不在加餐时间段内，找到下一个即将到来的时间段
    nextPeriod = SNACK_PERIODS.find(period => currentTimeInMinutes < period.startHour * 60) || SNACK_PERIODS[0];
  }

  // 计算距离下一个时间段的时间
  const timeUntilNext = calculateTimeUntilNext(currentTime, nextPeriod);

  return {
    currentPeriod,
    nextPeriod,
    isSnackTime: !!currentPeriod,
    timeUntilNext
  };
}

/**
 * 计算距离下一个加餐时间段的时间
 */
function calculateTimeUntilNext(currentTime: Date, nextPeriod: SnackPeriod | null): string {
  if (!nextPeriod) return '';

  const now = new Date(currentTime);
  const nextStart = new Date(now);
  nextStart.setHours(nextPeriod.startHour, 0, 0, 0);

  // 如果下一个时间段是明天的
  if (nextStart <= now) {
    nextStart.setDate(nextStart.getDate() + 1);
  }

  const diffMs = nextStart.getTime() - now.getTime();
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

  if (diffHours > 0) {
    return `${diffHours}小时${diffMinutes}分钟后`;
  } else {
    return `${diffMinutes}分钟后`;
  }
}

/**
 * 计算加餐卡路里分配
 * @param dailyCalorieLimit 每日卡路里限额
 * @param snackPercentage 加餐占总卡路里的百分比 (默认12%)
 */
export function calculateSnackCalories(dailyCalorieLimit: number, snackPercentage: number = 0.12): {
  totalSnackCalories: number;
  perSnackCalories: number;
} {
  const totalSnackCalories = Math.round(dailyCalorieLimit * snackPercentage);
  const perSnackCalories = Math.round(totalSnackCalories / SNACK_PERIODS.length);
  
  return {
    totalSnackCalories,
    perSnackCalories
  };
}

/**
 * 检查当前是否可以选择加餐
 */
export function canSelectSnack(currentTime: Date = new Date()): boolean {
  const snackInfo = getCurrentSnackTimeInfo(currentTime);
  return snackInfo.isSnackTime;
}

/**
 * 获取当前时间应该选择的餐次类型
 */
export function getCurrentMealType(currentTime: Date = new Date()): 'breakfast' | 'lunch' | 'dinner' | 'morning-snack' | 'afternoon-snack' | 'evening-snack' {
  const hour = currentTime.getHours();

  // 添加调试日志
  console.log(`当前时间判断 - 小时: ${hour}, 完整时间: ${currentTime.toLocaleTimeString()}`);

  // 优先检查加餐时间段（因为加餐时间段可能与主餐时间重叠）
  const snackInfo = getCurrentSnackTimeInfo(currentTime);
  if (snackInfo.isSnackTime && snackInfo.currentPeriod) {
    console.log(`检测到加餐时间段: ${snackInfo.currentPeriod.name} (${snackInfo.currentPeriod.timeRange})`);
    switch (snackInfo.currentPeriod.id) {
      case 'morning':
        return 'morning-snack';
      case 'afternoon':
        return 'afternoon-snack';
      case 'evening':
        return 'evening-snack';
      default:
        return 'morning-snack'; // 默认上午加餐
    }
  }

  // 然后判断主餐时间
  // 早餐：0:00-11:00（包含深夜到早上的完整时间段）
  if ((hour >= 0 && hour < 6) || (hour >= 6 && hour < 11)) {
    console.log('判断为早餐时间');
    return 'breakfast';
  }

  // 午餐：11:00-17:00
  if (hour >= 11 && hour < 17) {
    console.log('判断为午餐时间');
    return 'lunch';
  }

  // 晚餐：17:00-21:00
  if (hour >= 17 && hour < 21) {
    console.log('判断为晚餐时间');
    return 'dinner';
  }

  // 21:00-24:00 如果不是晚间加餐时间，则归为晚餐
  if (hour >= 21) {
    console.log('21:00后非加餐时间，判断为晚餐');
    return 'dinner';
  }

  // 默认早餐
  console.log('默认判断为早餐');
  return 'breakfast';
}

/**
 * 获取加餐建议文本
 */
export function getSnackRecommendation(
  snackInfo: SnackTimeInfo,
  mainMealsCompleted: boolean,
  remainingCalories: number
): string {
  if (!snackInfo.isSnackTime) {
    return `${snackInfo.nextPeriod?.name}将在${snackInfo.timeUntilNext}开始`;
  }

  if (!mainMealsCompleted && remainingCalories > 200) {
    return '建议优先完成主餐摄入';
  }

  return `现在是${snackInfo.currentPeriod?.name}时间，适合补充营养`;
}
