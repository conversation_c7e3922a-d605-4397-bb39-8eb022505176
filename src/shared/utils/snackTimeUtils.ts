/**
 * 加餐时间段管理工具
 */

export interface SnackPeriod {
  id: 'morning' | 'afternoon' | 'evening';
  name: string;
  emoji: string;
  timeRange: string;
  startHour: number;
  endHour: number;
  description: string;
}

export interface SnackTimeInfo {
  currentPeriod: SnackPeriod | null;
  nextPeriod: SnackPeriod | null;
  isSnackTime: boolean;
  timeUntilNext: string;
}

// 定义加餐时间段
export const SNACK_PERIODS: SnackPeriod[] = [
  {
    id: 'morning',
    name: '上午加餐',
    emoji: '🥐',
    timeRange: '10:00-11:00',
    startHour: 10,
    endHour: 11,
    description: '早餐后补充'
  },
  {
    id: 'afternoon',
    name: '下午加餐',
    emoji: '🍎',
    timeRange: '14:00-17:00',
    startHour: 14,
    endHour: 17,
    description: '午后能量'
  },
  {
    id: 'evening',
    name: '晚间加餐',
    emoji: '🥛',
    timeRange: '21:00-23:00',
    startHour: 21,
    endHour: 23,
    description: '睡前小食'
  }
];

/**
 * 获取当前时间的加餐信息
 */
export function getCurrentSnackTimeInfo(currentTime: Date = new Date()): SnackTimeInfo {
  const currentHour = currentTime.getHours();
  
  // 查找当前时间段
  const currentPeriod = SNACK_PERIODS.find(period => 
    currentHour >= period.startHour && currentHour < period.endHour
  );

  // 查找下一个时间段
  let nextPeriod: SnackPeriod | null = null;
  
  if (currentPeriod) {
    // 如果在加餐时间段内，下一个时间段是下一个加餐时间
    const currentIndex = SNACK_PERIODS.findIndex(p => p.id === currentPeriod.id);
    nextPeriod = SNACK_PERIODS[(currentIndex + 1) % SNACK_PERIODS.length];
  } else {
    // 如果不在加餐时间段内，找到下一个即将到来的时间段
    nextPeriod = SNACK_PERIODS.find(period => currentHour < period.startHour) || SNACK_PERIODS[0];
  }

  // 计算距离下一个时间段的时间
  const timeUntilNext = calculateTimeUntilNext(currentTime, nextPeriod);

  return {
    currentPeriod,
    nextPeriod,
    isSnackTime: !!currentPeriod,
    timeUntilNext
  };
}

/**
 * 计算距离下一个加餐时间段的时间
 */
function calculateTimeUntilNext(currentTime: Date, nextPeriod: SnackPeriod | null): string {
  if (!nextPeriod) return '';

  const now = new Date(currentTime);
  const nextStart = new Date(now);
  nextStart.setHours(nextPeriod.startHour, 0, 0, 0);

  // 如果下一个时间段是明天的
  if (nextStart <= now) {
    nextStart.setDate(nextStart.getDate() + 1);
  }

  const diffMs = nextStart.getTime() - now.getTime();
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

  if (diffHours > 0) {
    return `${diffHours}小时${diffMinutes}分钟后`;
  } else {
    return `${diffMinutes}分钟后`;
  }
}

/**
 * 计算加餐卡路里分配
 * @param dailyCalorieLimit 每日卡路里限额
 * @param snackPercentage 加餐占总卡路里的百分比 (默认12%)
 */
export function calculateSnackCalories(dailyCalorieLimit: number, snackPercentage: number = 0.12): {
  totalSnackCalories: number;
  perSnackCalories: number;
} {
  const totalSnackCalories = Math.round(dailyCalorieLimit * snackPercentage);
  const perSnackCalories = Math.round(totalSnackCalories / SNACK_PERIODS.length);
  
  return {
    totalSnackCalories,
    perSnackCalories
  };
}

/**
 * 获取加餐建议文本
 */
export function getSnackRecommendation(
  snackInfo: SnackTimeInfo,
  mainMealsCompleted: boolean,
  remainingCalories: number
): string {
  if (!snackInfo.isSnackTime) {
    return `${snackInfo.nextPeriod?.name}将在${snackInfo.timeUntilNext}开始`;
  }

  if (!mainMealsCompleted && remainingCalories > 200) {
    return '建议优先完成主餐摄入';
  }

  return `现在是${snackInfo.currentPeriod?.name}时间，适合补充营养`;
}
