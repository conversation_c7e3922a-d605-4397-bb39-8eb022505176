import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

interface FoodBottomNavigationProps {
  onAddText?: () => void;
  onAddImage?: () => void;
  showAddButton?: boolean;
}

const FoodBottomNavigation: React.FC<FoodBottomNavigationProps> = ({
  onAddText,
  onAddImage,
  showAddButton = true
}) => {
  const navigate = useNavigate();
  const location = useLocation();

  const isActive = (path: string) => location.pathname === path;

  return (
    <div
      className="dock fixed bottom-0 left-0 right-0 bg-base-100"
      style={{
        position: 'fixed',
        zIndex: 1000,
        transform: 'none'
      }}
    >
        {/* 首页 */}
        <button
          onClick={() => navigate('/dashboard')}
          className={isActive('/dashboard') ? 'dock-active' : ''}
          aria-label="首页"
        >
          <span className="text-lg">🏠</span>
          <span className="dock-label">首页</span>
        </button>
        
        {/* 记录 */}
        <button
          onClick={() => navigate('/food-record')}
          className={isActive('/food-record') ? 'dock-active' : ''}
          aria-label="开始记录食物"
        >
          <span className="text-lg">🍽️</span>
          <span className="dock-label">记录</span>
        </button>
        
        {/* 中间添加按钮 */}
        {showAddButton && (onAddText || onAddImage) && (
          <div className="dropdown dropdown-top dropdown-center">
            <button tabIndex={0} role="button" aria-label="添加食物">
              <span className="text-lg">➕</span>
              <span className="dock-label">添加</span>
            </button>
            <ul tabIndex={0} className="dropdown-content menu bg-white rounded-box z-[1] w-48 p-1 shadow-lg border border-slate-200 mb-2">
              {onAddText && (
                <li>
                  <button onClick={onAddText} className="flex items-center gap-2 py-2 px-3">
                    <span className="text-base">📝</span>
                    <div className="text-left">
                      <div className="font-medium text-sm">文字识别</div>
                      <div className="text-xs text-slate-500">描述食物内容</div>
                    </div>
                  </button>
                </li>
              )}
              {onAddImage && (
                <li>
                  <button onClick={onAddImage} className="flex items-center gap-2 py-2 px-3">
                    <span className="text-base">📷</span>
                    <div className="text-left">
                      <div className="font-medium text-sm">拍照识别</div>
                      <div className="text-xs text-slate-500">AI识别食物</div>
                    </div>
                  </button>
                </li>
              )}
            </ul>
          </div>
        )}
        
        {/* 如果不显示添加按钮，显示占位符 */}
        {!showAddButton && (
          <button disabled aria-label="添加食物">
            <span className="text-lg opacity-30">➕</span>
            <span className="dock-label opacity-30">添加</span>
          </button>
        )}
        
        {/* 日历 */}
        <button
          onClick={() => navigate('/calendar')}
          className={isActive('/calendar') ? 'dock-active' : ''}
          aria-label="查看日历"
        >
          <span className="text-lg">📅</span>
          <span className="dock-label">日历</span>
        </button>

        {/* 我的 */}
        <button
          onClick={() => navigate('/profile')}
          className={isActive('/profile') ? 'dock-active' : ''}
          aria-label="我的"
        >
          <span className="text-lg">👤</span>
          <span className="dock-label">我的</span>
        </button>
    </div>
  );
};

export default FoodBottomNavigation;
