import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import FoodRecognitionModal, { RecognitionMethod } from '../modals/FoodRecognitionModal';

interface BottomNavigationProps {
  showAddButton?: boolean;
  onRecognitionComplete?: (result: any) => void;
}

const BottomNavigation: React.FC<BottomNavigationProps> = ({ 
  showAddButton = true,
  onRecognitionComplete
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMethod, setModalMethod] = useState<RecognitionMethod>('text');

  const isActive = (path: string) => location.pathname === path;

  // 处理添加按钮点击
  const handleAddText = () => {
    setModalMethod('text');
    setIsModalOpen(true);
    // 关闭下拉菜单
    const dropdown = document.activeElement as HTMLElement;
    dropdown?.blur();
  };

  const handleAddImage = () => {
    setModalMethod('image');
    setIsModalOpen(true);
    // 关闭下拉菜单
    const dropdown = document.activeElement as HTMLElement;
    dropdown?.blur();
  };

  // 处理识别完成
  const handleRecognitionComplete = (result: any) => {
    onRecognitionComplete?.(result);
    setIsModalOpen(false);
  };

  return (
    <>
      <div
        className="dock fixed bottom-0 left-0 right-0 bg-base-100"
        style={{
          position: 'fixed',
          zIndex: 1000,
          transform: 'none'
        }}
      >
        {/* 首页 */}
        <button
          onClick={() => navigate('/dashboard')}
          className={isActive('/dashboard') ? 'dock-active' : ''}
          aria-label="首页"
        >
          <span className="text-lg">🏠</span>
          <span className="dock-label">首页</span>
        </button>
        
        {/* 记录 */}
        <button
          onClick={() => navigate('/food-record')}
          className={isActive('/food-record') ? 'dock-active' : ''}
          aria-label="开始记录食物"
        >
          <span className="text-lg">🍽️</span>
          <span className="dock-label">记录</span>
        </button>
        
        {/* 中间添加按钮 */}
        {showAddButton && (
          <div className="dropdown dropdown-top dropdown-center">
            <button tabIndex={0} role="button" aria-label="添加食物">
              <span className="text-lg">➕</span>
              <span className="dock-label">添加</span>
            </button>
            <ul tabIndex={0} className="dropdown-content menu bg-white rounded-box z-[1] w-auto min-w-fit p-1 shadow-lg border border-slate-200 mb-2">
              <li>
                <button onClick={handleAddText} className="flex items-center justify-center gap-2 py-2 px-4">
                  <span className="text-base">📝</span>
                  <div className="font-medium text-sm text-center">文字识别</div>
                </button>
              </li>
              <li>
                <button onClick={handleAddImage} className="flex items-center justify-center gap-2 py-2 px-4">
                  <span className="text-base">📷</span>
                  <div className="font-medium text-sm text-center">拍照识别</div>
                </button>
              </li>
            </ul>
          </div>
        )}
        
        {/* 如果不显示添加按钮，显示占位符 */}
        {!showAddButton && (
          <button disabled aria-label="添加食物">
            <span className="text-lg opacity-30">➕</span>
            <span className="dock-label opacity-30">添加</span>
          </button>
        )}
        
        {/* 日历 */}
        <button
          onClick={() => navigate('/calendar')}
          className={isActive('/calendar') ? 'dock-active' : ''}
          aria-label="查看日历"
        >
          <span className="text-lg">📅</span>
          <span className="dock-label">日历</span>
        </button>
        
        {/* 我的 */}
        <button
          onClick={() => navigate('/profile')}
          className={isActive('/profile') ? 'dock-active' : ''}
          aria-label="我的"
        >
          <span className="text-lg">👤</span>
          <span className="dock-label">我的</span>
        </button>
      </div>

      {/* 识别弹窗 */}
      <FoodRecognitionModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        method={modalMethod}
        onRecognitionComplete={handleRecognitionComplete}
      />
    </>
  );
};

export default BottomNavigation;
