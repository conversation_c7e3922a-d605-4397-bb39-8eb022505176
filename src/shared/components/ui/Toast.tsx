import React, { useEffect, useState } from 'react';

export interface ToastProps {
  message: string;
  type: 'success' | 'error' | 'info' | 'warning';
  duration?: number;
  onClose: () => void;
}

const Toast: React.FC<ToastProps> = ({ message, type, duration = 3000, onClose }) => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(onClose, 300); // 等待动画完成
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  const getToastClass = () => {
    const baseClass = 'alert shadow-lg transition-all duration-300 transform';
    const typeClass = {
      success: 'alert-success',
      error: 'alert-error',
      info: 'alert-info',
      warning: 'alert-warning'
    }[type];
    
    const visibilityClass = isVisible 
      ? 'translate-y-0 opacity-100' 
      : '-translate-y-2 opacity-0';
    
    return `${baseClass} ${typeClass} ${visibilityClass}`;
  };

  const getIcon = () => {
    switch (type) {
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'info':
      default:
        return 'ℹ️';
    }
  };

  return (
    <div className="fixed top-4 right-4 z-[9999] max-w-sm">
      <div className={getToastClass()}>
        <div className="flex items-center gap-2">
          <span className="text-lg">{getIcon()}</span>
          <span className="text-sm font-medium">{message}</span>
        </div>
        <button
          onClick={() => {
            setIsVisible(false);
            setTimeout(onClose, 300);
          }}
          className="btn btn-ghost btn-xs btn-circle ml-2"
        >
          ✕
        </button>
      </div>
    </div>
  );
};

export default Toast;
