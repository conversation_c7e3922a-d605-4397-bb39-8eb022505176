import React, { useState, useRef } from 'react';

export type RecognitionMethod = 'text' | 'image';
export type MealType = 'breakfast' | 'lunch' | 'dinner' | 'snack';

interface FoodRecognitionModalProps {
  isOpen: boolean;
  onClose: () => void;
  method: RecognitionMethod;
  onRecognitionComplete?: (result: any) => void;
}

const FoodRecognitionModal: React.FC<FoodRecognitionModalProps> = ({
  isOpen,
  onClose,
  method,
  onRecognitionComplete
}) => {
  const [selectedMeal, setSelectedMeal] = useState<MealType>('snack');
  const [textInput, setTextInput] = useState('');
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 获取当前时间对应的餐次
  const getCurrentMealType = (): MealType => {
    const hour = new Date().getHours();
    if (hour >= 6 && hour < 10) return 'breakfast';
    if (hour >= 11 && hour < 14) return 'lunch';
    if (hour >= 17 && hour < 21) return 'dinner';
    return 'snack';
  };

  // 重置状态
  const resetState = () => {
    setSelectedMeal(getCurrentMealType());
    setTextInput('');
    setSelectedImage(null);
    setImagePreview(null);
    setIsProcessing(false);
    setError(null);
  };

  // 处理弹窗关闭
  const handleClose = () => {
    if (!isProcessing) {
      resetState();
      onClose();
    }
  };

  // 处理图片选择
  const handleImageSelect = (file: File) => {
    setSelectedImage(file);
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  // 处理拍照
  const handleCameraCapture = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // 处理文件上传
  const handleFileUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // 开始识别
  const handleStartRecognition = async () => {
    setIsProcessing(true);
    setError(null);

    try {
      // 模拟识别过程
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const result = {
        method,
        meal: selectedMeal,
        content: method === 'text' ? textInput : selectedImage?.name,
        timestamp: new Date().toISOString()
      };

      onRecognitionComplete?.(result);
      handleClose();
    } catch (err) {
      setError('识别失败，请重试');
    } finally {
      setIsProcessing(false);
    }
  };

  // 终止识别
  const handleStopRecognition = () => {
    setIsProcessing(false);
    setError(null);
  };

  // 检查是否可以开始识别
  const canStartRecognition = () => {
    if (method === 'text') {
      return textInput.trim().length > 0;
    }
    return selectedImage !== null;
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50 p-4"
      style={{
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
        backdropFilter: 'blur(4px)'
      }}
      onClick={handleClose}
    >
      <div
        className="bg-white rounded-lg w-full max-w-md max-h-[90vh] overflow-y-auto shadow-2xl"
        onClick={(e) => e.stopPropagation()}
      >
        {/* 弹窗头部 */}
        <div className="flex items-center justify-between p-4 border-b border-slate-200">
          <h2 className="text-lg font-bold text-slate-800">
            {method === 'text' ? '对话识别' : '图片识别'}
          </h2>
          <button
            onClick={handleClose}
            className="btn btn-ghost btn-sm btn-circle"
            disabled={isProcessing}
          >
            ✕
          </button>
        </div>

        {/* 弹窗内容 */}
        <div className="p-4 space-y-4">
          {/* 餐次选择 */}
          <div>
            <h3 className="text-base font-medium text-slate-800 mb-2">选择餐次</h3>
            <select
              value={selectedMeal}
              onChange={(e) => setSelectedMeal(e.target.value as MealType)}
              className="select select-bordered w-full"
              disabled={isProcessing}
            >
              <option value="breakfast">🌅 早餐 (6:00-10:00)</option>
              <option value="lunch">☀️ 午餐 (11:00-14:00)</option>
              <option value="dinner">🌙 晚餐 (17:00-21:00)</option>
              <option value="snack">🍎 加餐 (随时)</option>
            </select>
          </div>

          {/* 内容输入区域 */}
          {method === 'text' ? (
            <div>
              <h3 className="text-base font-medium text-slate-800 mb-2">描述食物</h3>
              <textarea
                value={textInput}
                onChange={(e) => setTextInput(e.target.value)}
                placeholder="例如：一碗白米饭，一个苹果，200ml牛奶..."
                className="textarea textarea-bordered w-full h-32 resize-none"
                disabled={isProcessing}
              />
              <div className="text-xs text-slate-500 mt-2">
                💡 提示：描述越详细，AI识别越准确
              </div>
            </div>
          ) : (
            <div>
              <h3 className="text-base font-medium text-slate-800 mb-2">选择图片</h3>
              {!imagePreview ? (
                <div className="grid grid-cols-2 gap-3">
                  <button
                    onClick={handleCameraCapture}
                    className="btn btn-outline flex flex-col items-center gap-2 h-20"
                    disabled={isProcessing}
                  >
                    <span className="text-2xl">📷</span>
                    <span className="text-sm">拍照识别</span>
                  </button>
                  <button
                    onClick={handleFileUpload}
                    className="btn btn-outline flex flex-col items-center gap-2 h-20"
                    disabled={isProcessing}
                  >
                    <span className="text-2xl">📁</span>
                    <span className="text-sm">从相册选择</span>
                  </button>
                </div>
              ) : (
                <div className="space-y-3">
                  <div className="relative">
                    <img
                      src={imagePreview}
                      alt="预览"
                      className="w-full h-48 object-cover rounded-lg border border-slate-200"
                    />
                    <button
                      onClick={() => {
                        setSelectedImage(null);
                        setImagePreview(null);
                      }}
                      className="absolute top-2 right-2 btn btn-ghost btn-sm btn-circle bg-white"
                      disabled={isProcessing}
                    >
                      ✕
                    </button>
                  </div>
                  <div className="text-sm text-slate-600">
                    已选择图片：{selectedImage?.name}
                  </div>
                </div>
              )}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                capture="environment"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) handleImageSelect(file);
                }}
                className="hidden"
              />
            </div>
          )}

          {/* 错误提示 */}
          {error && (
            <div className="alert alert-error">
              <span>{error}</span>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex gap-3 pt-2">
            {!isProcessing ? (
              <button
                onClick={handleStartRecognition}
                disabled={!canStartRecognition()}
                className="btn btn-primary flex-1"
              >
                开始识别
              </button>
            ) : (
              <button
                onClick={handleStopRecognition}
                className="btn btn-error flex-1"
              >
                终止识别
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FoodRecognitionModal;
