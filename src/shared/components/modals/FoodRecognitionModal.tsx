import React, { useState, useRef } from 'react';
import { getCurrentSnackTimeInfo, canSelectSnack, getCurrentMealType } from '@/shared/utils/snackTimeUtils';
import { useFoodRecognition } from '@/shared/hooks/useFoodRecognition';

export type RecognitionMethod = 'text' | 'image';
export type MealType = 'breakfast' | 'lunch' | 'dinner' | 'snack';

interface FoodRecognitionModalProps {
  isOpen: boolean;
  onClose: () => void;
  method: RecognitionMethod;
  onRecognitionComplete?: (result: any) => void;
}

const FoodRecognitionModal: React.FC<FoodRecognitionModalProps> = ({
  isOpen,
  onClose,
  method,
  onRecognitionComplete
}) => {
  // 移除手动餐次选择，改为自动判断
  const [textInput, setTextInput] = useState('');
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { state, startRecognition, stopRecognition } = useFoodRecognition();

  // 获取当前时间对应的餐次（使用工具函数）
  const getDefaultMealType = (): MealType => {
    return getCurrentMealType();
  };

  // 重置状态
  const resetState = () => {
    setTextInput('');
    setSelectedImage(null);
    setImagePreview(null);
  };

  // 处理弹窗关闭
  const handleClose = () => {
    if (!state.isProcessing) {
      resetState();
      onClose();
    }
  };

  // 处理图片选择
  const handleImageSelect = (file: File) => {
    setSelectedImage(file);
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  // 处理拍照
  const handleCameraCapture = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // 处理文件上传
  const handleFileUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // 开始识别
  const handleStartRecognition = async () => {
    // 自动判断当前时间对应的餐次
    const autoSelectedMeal = getCurrentMealType();

    await startRecognition(
      method,
      autoSelectedMeal,
      textInput,
      selectedImage,
      (result) => {
        onRecognitionComplete?.(result);
        handleClose();
      }
    );
  };

  // 终止识别
  const handleStopRecognition = () => {
    stopRecognition();
  };

  // 检查是否可以开始识别
  const canStartRecognition = () => {
    if (method === 'text') {
      return textInput.trim().length > 0;
    }
    return selectedImage !== null;
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50 p-4 pb-20"
      style={{
        backgroundColor: 'rgba(0, 0, 0, 0.4)',
        backdropFilter: 'blur(8px)'
      }}
      onClick={handleClose}
    >
      <div
        className="relative bg-white rounded-2xl w-full max-w-sm sm:max-w-md max-h-[80vh] sm:max-h-[75vh] overflow-hidden shadow-2xl border border-gray-100 flex flex-col"
        style={{
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* 弹窗头部 */}
        <div className="flex items-center justify-between p-6 pb-4">
          <div>
            <h2 className="text-xl font-bold text-gray-900 mb-1">
              {method === 'text' ? '对话识别' : '图片识别'}
            </h2>
            <p className="text-sm text-gray-500">
              {method === 'text' ? '通过对话描述识别食物' : '通过拍照识别食物'}
            </p>
          </div>
          <button
            onClick={handleClose}
            className="btn btn-ghost btn-sm btn-circle hover:bg-gray-100 transition-colors"
            disabled={state.isProcessing}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 弹窗内容 */}
        <div className="flex-1 overflow-y-auto p-6 pt-2 space-y-6 pb-20">
          {/* 自动餐次提示 */}
          <div>
            <h3 className="text-base font-medium text-slate-800 mb-2">识别餐次</h3>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="flex items-center gap-2">
                <span className="text-lg">
                  {(() => {
                    const mealType = getCurrentMealType();
                    const mealConfig = {
                      breakfast: '🌅',
                      lunch: '☀️',
                      dinner: '🌙',
                      snack: (() => {
                        const snackInfo = getCurrentSnackTimeInfo();
                        return snackInfo.currentPeriod?.emoji || '🍎';
                      })()
                    };
                    return mealConfig[mealType];
                  })()}
                </span>
                <div>
                  <div className="text-sm font-medium text-blue-800">
                    {(() => {
                      const mealType = getCurrentMealType();
                      const mealNames = {
                        breakfast: '早餐',
                        lunch: '午餐',
                        dinner: '晚餐',
                        snack: (() => {
                          const snackInfo = getCurrentSnackTimeInfo();
                          return snackInfo.currentPeriod?.name || '加餐';
                        })()
                      };
                      return `将自动归类到：${mealNames[mealType]}`;
                    })()}
                  </div>
                  <div className="text-xs text-blue-600">
                    根据当前时间自动判断
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 内容输入区域 */}
          {method === 'text' ? (
            <div>
              <h3 className="text-base font-medium text-slate-800 mb-2">描述食物</h3>
              <textarea
                value={textInput}
                onChange={(e) => setTextInput(e.target.value)}
                placeholder="例如：一碗白米饭，一个苹果，200ml牛奶..."
                className="textarea textarea-bordered w-full h-32 resize-none"
                disabled={state.isProcessing}
              />
              <div className="text-xs text-slate-500 mt-2">
                💡 提示：描述越详细，AI识别越准确
              </div>
            </div>
          ) : (
            <div>
              <h3 className="text-base font-medium text-slate-800 mb-2">选择图片</h3>
              {!imagePreview ? (
                <div className="grid grid-cols-2 gap-3">
                  <button
                    onClick={handleCameraCapture}
                    className="btn btn-outline flex flex-col items-center gap-2 h-20"
                    disabled={state.isProcessing}
                  >
                    <span className="text-2xl">📷</span>
                    <span className="text-sm">拍照识别</span>
                  </button>
                  <button
                    onClick={handleFileUpload}
                    className="btn btn-outline flex flex-col items-center gap-2 h-20"
                    disabled={state.isProcessing}
                  >
                    <span className="text-2xl">📁</span>
                    <span className="text-sm">从相册选择</span>
                  </button>
                </div>
              ) : (
                <div className="space-y-3">
                  <div className="relative">
                    <img
                      src={imagePreview}
                      alt="预览"
                      className="w-full h-48 object-cover rounded-lg border border-slate-200"
                    />
                    <button
                      onClick={() => {
                        setSelectedImage(null);
                        setImagePreview(null);
                      }}
                      className="absolute top-2 right-2 btn btn-ghost btn-sm btn-circle bg-white"
                      disabled={state.isProcessing}
                    >
                      ✕
                    </button>
                  </div>
                  <div className="text-sm text-slate-600">
                    已选择图片：{selectedImage?.name}
                  </div>
                </div>
              )}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                capture="environment"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) handleImageSelect(file);
                }}
                className="hidden"
              />
            </div>
          )}

          {/* 错误提示 */}
          {state.error && (
            <div className="alert alert-error">
              <span>{state.error}</span>
            </div>
          )}

          {/* 进度提示 */}
          {state.isProcessing && state.processingStep && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
              <div className="flex items-center gap-2">
                <div className="loading loading-spinner loading-sm text-blue-600"></div>
                <span className="text-sm text-blue-700 font-medium">{state.processingStep}</span>
              </div>
            </div>
          )}

        </div>

        {/* 操作按钮 */}
        <div className="absolute bottom-0 left-0 right-0 flex gap-3 p-6 pt-4 bg-gray-50 rounded-b-2xl border-t border-gray-100">
          {!state.isProcessing ? (
            <button
              onClick={handleStartRecognition}
              disabled={!canStartRecognition()}
              className="btn btn-primary text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              开始识别
            </button>
          ) : (
            <button
              onClick={handleStopRecognition}
              className="btn btn-error text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
              终止识别
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default FoodRecognitionModal;
