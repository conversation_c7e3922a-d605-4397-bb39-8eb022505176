import React, { useState } from 'react';
import { FoodRecord } from '@/shared/types';

interface FoodNutritionModalProps {
  isOpen: boolean;
  onClose: () => void;
  foodRecord: FoodRecord | null;
  onSave: (updatedRecord: FoodRecord) => void;
}

const FoodNutritionModal: React.FC<FoodNutritionModalProps> = ({
  isOpen,
  onClose,
  foodRecord,
  onSave
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedRecord, setEditedRecord] = useState<FoodRecord | null>(null);

  // 初始化编辑状态
  React.useEffect(() => {
    if (foodRecord) {
      setEditedRecord({ ...foodRecord });
    }
  }, [foodRecord]);

  if (!isOpen || !foodRecord || !editedRecord) return null;

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    if (editedRecord) {
      const updatedRecord = {
        ...editedRecord,
        isEdited: true,
        editedAt: new Date()
      };
      onSave(updatedRecord);
      setIsEditing(false);
      onClose();
    }
  };

  const handleCancel = () => {
    setEditedRecord({ ...foodRecord });
    setIsEditing(false);
  };

  const handleInputChange = (field: string, value: string | number) => {
    if (!editedRecord) return;

    if (field.startsWith('nutrition.')) {
      const nutritionField = field.split('.')[1];
      setEditedRecord({
        ...editedRecord,
        nutrition: {
          ...editedRecord.nutrition,
          [nutritionField]: typeof value === 'string' ? parseFloat(value) || 0 : value
        }
      });
    } else {
      setEditedRecord({
        ...editedRecord,
        [field]: typeof value === 'string' && (field === 'calories' || field === 'weight') 
          ? parseFloat(value) || 0 
          : value
      });
    }
  };

  const formatTime = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getAccuracyColor = (accuracy: number) => {
    if (accuracy >= 0.8) return 'text-green-600';
    if (accuracy >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getAccuracyText = (accuracy: number) => {
    if (accuracy >= 0.8) return '高准确度';
    if (accuracy >= 0.6) return '中等准确度';
    return '低准确度';
  };

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50 p-4"
      style={{
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
        backdropFilter: 'blur(4px)'
      }}
      onClick={() => {
        if (!isEditing) {
          onClose();
        }
      }}
    >
      <div
        className="bg-white rounded-lg w-full max-w-sm sm:max-w-md max-h-[90vh] sm:max-h-[85vh] overflow-y-auto shadow-2xl"
        onClick={(e) => e.stopPropagation()}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b border-slate-200">
          <h2 className="text-lg font-bold text-slate-800">营养详情</h2>
          <button
            onClick={onClose}
            className="btn btn-ghost btn-sm btn-circle"
            disabled={isEditing}
          >
            ✕
          </button>
        </div>

        {/* 内容 */}
        <div className="p-4 space-y-4">
          {/* 基础信息 */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-3 border border-blue-200">
            <h3 className="text-base font-semibold text-blue-800 mb-2">基础信息</h3>
            
            <div className="space-y-2">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">食物名称</label>
                {isEditing ? (
                  <input
                    type="text"
                    value={editedRecord.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="input input-bordered w-full"
                  />
                ) : (
                  <div className="text-lg font-medium text-gray-900">{foodRecord.name}</div>
                )}
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">重量 (g)</label>
                  {isEditing ? (
                    <input
                      type="number"
                      value={editedRecord.weight}
                      onChange={(e) => handleInputChange('weight', e.target.value)}
                      className="input input-bordered w-full"
                      min="0"
                      step="0.1"
                    />
                  ) : (
                    <div className="text-lg font-medium text-gray-900">{foodRecord.weight}g</div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">卡路里</label>
                  {isEditing ? (
                    <input
                      type="number"
                      value={editedRecord.calories}
                      onChange={(e) => handleInputChange('calories', e.target.value)}
                      className="input input-bordered w-full"
                      min="0"
                      step="1"
                    />
                  ) : (
                    <div className="text-lg font-medium text-gray-900">{foodRecord.calories} kcal</div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* 营养成分 */}
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-3 border border-green-200">
            <h3 className="text-base font-semibold text-green-800 mb-2">营养成分</h3>
            
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">蛋白质 (g)</label>
                {isEditing ? (
                  <input
                    type="number"
                    value={editedRecord.nutrition.protein}
                    onChange={(e) => handleInputChange('nutrition.protein', e.target.value)}
                    className="input input-bordered w-full"
                    min="0"
                    step="0.1"
                  />
                ) : (
                  <div className="text-lg font-medium text-gray-900">{foodRecord.nutrition.protein}g</div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">脂肪 (g)</label>
                {isEditing ? (
                  <input
                    type="number"
                    value={editedRecord.nutrition.fat}
                    onChange={(e) => handleInputChange('nutrition.fat', e.target.value)}
                    className="input input-bordered w-full"
                    min="0"
                    step="0.1"
                  />
                ) : (
                  <div className="text-lg font-medium text-gray-900">{foodRecord.nutrition.fat}g</div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">碳水化合物 (g)</label>
                {isEditing ? (
                  <input
                    type="number"
                    value={editedRecord.nutrition.carbs}
                    onChange={(e) => handleInputChange('nutrition.carbs', e.target.value)}
                    className="input input-bordered w-full"
                    min="0"
                    step="0.1"
                  />
                ) : (
                  <div className="text-lg font-medium text-gray-900">{foodRecord.nutrition.carbs}g</div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">纤维 (g)</label>
                {isEditing ? (
                  <input
                    type="number"
                    value={editedRecord.nutrition.fiber}
                    onChange={(e) => handleInputChange('nutrition.fiber', e.target.value)}
                    className="input input-bordered w-full"
                    min="0"
                    step="0.1"
                  />
                ) : (
                  <div className="text-lg font-medium text-gray-900">{foodRecord.nutrition.fiber}g</div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">糖分 (g)</label>
                {isEditing ? (
                  <input
                    type="number"
                    value={editedRecord.nutrition.sugar}
                    onChange={(e) => handleInputChange('nutrition.sugar', e.target.value)}
                    className="input input-bordered w-full"
                    min="0"
                    step="0.1"
                  />
                ) : (
                  <div className="text-lg font-medium text-gray-900">{foodRecord.nutrition.sugar}g</div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">钠 (mg)</label>
                {isEditing ? (
                  <input
                    type="number"
                    value={editedRecord.nutrition.sodium || 0}
                    onChange={(e) => handleInputChange('nutrition.sodium', e.target.value)}
                    className="input input-bordered w-full"
                    min="0"
                    step="1"
                  />
                ) : (
                  <div className="text-lg font-medium text-gray-900">{foodRecord.nutrition.sodium || 0}mg</div>
                )}
              </div>
            </div>
          </div>

          {/* 记录信息 */}
          <div className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-lg p-3 border border-gray-200">
            <h3 className="text-base font-semibold text-gray-800 mb-2">记录信息</h3>

            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">添加时间：</span>
                <span className="font-medium">{formatTime(foodRecord.recordedAt)}</span>
              </div>

              {foodRecord.aiRecognition && (
                <>
                  <div className="flex justify-between">
                    <span className="text-gray-600">识别方法：</span>
                    <span className="font-medium">
                      {foodRecord.aiRecognition.method === 'text' ? '文字识别' : '图像识别'}
                    </span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-gray-600">准确度：</span>
                    <span className={`font-medium ${getAccuracyColor(foodRecord.aiRecognition.confidence)}`}>
                      {(foodRecord.aiRecognition.confidence * 100).toFixed(0)}% ({getAccuracyText(foodRecord.aiRecognition.confidence)})
                    </span>
                  </div>
                </>
              )}

              {foodRecord.isEdited && foodRecord.editedAt && (
                <div className="flex justify-between">
                  <span className="text-gray-600">最后编辑：</span>
                  <span className="font-medium">{formatTime(foodRecord.editedAt)}</span>
                </div>
              )}

              {foodRecord.isEdited && (
                <div className="text-xs text-orange-600 bg-orange-50 rounded p-1 mt-1">
                  ⚠️ 此记录已被手动编辑
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="flex gap-3 p-4 border-t border-slate-200">
          {!isEditing ? (
            <>
              <button
                onClick={handleEdit}
                className="btn btn-primary text-white flex-1"
              >
                编辑
              </button>
              <button
                onClick={onClose}
                className="btn btn-outline flex-1"
              >
                关闭
              </button>
            </>
          ) : (
            <>
              <button
                onClick={handleSave}
                className="btn btn-success text-white flex-1"
              >
                保存
              </button>
              <button
                onClick={handleCancel}
                className="btn btn-outline flex-1"
              >
                取消
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default FoodNutritionModal;
