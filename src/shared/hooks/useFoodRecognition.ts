import { useState, useCallback } from 'react';
import { MealType } from '@/shared/types';
import { useToast } from '@/shared/hooks/useToast';

export interface FoodRecognitionResult {
  method: 'text' | 'image';
  meal: MealType;
  content: string | undefined;
  timestamp: string;
  foods: Array<{
    name: string;
    calories: number;
    quantity: string;
    timestamp: string;
  }>;
}

export interface FoodRecognitionState {
  isProcessing: boolean;
  processingStep: string;
  error: string | null;
}

export const useFoodRecognition = () => {
  const [state, setState] = useState<FoodRecognitionState>({
    isProcessing: false,
    processingStep: '',
    error: null
  });

  const { showSuccess, showError, showInfo } = useToast();

  const updateState = useCallback((updates: Partial<FoodRecognitionState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const startRecognition = useCallback(async (
    method: 'text' | 'image',
    selectedMeal: MealType,
    textInput: string,
    selectedImage: File | null,
    onComplete: (result: FoodRecognitionResult) => void
  ) => {
    updateState({
      isProcessing: true,
      error: null,
      processingStep: '正在分析食物...'
    });

    try {
      // 模拟识别过程的不同阶段
      updateState({ processingStep: '正在分析食物...' });
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      updateState({ processingStep: '正在计算营养成分...' });
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      updateState({ processingStep: '正在生成记录...' });
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 生成识别结果
      const result: FoodRecognitionResult = {
        method,
        meal: selectedMeal,
        content: method === 'text' ? textInput : selectedImage?.name,
        timestamp: new Date().toISOString(),
        foods: [
          { 
            name: method === 'text' ? textInput.split('，')[0] || '食物' : '识别食物', 
            calories: Math.floor(Math.random() * 200) + 50, 
            quantity: '1份',
            timestamp: new Date().toISOString()
          }
        ]
      };

      showSuccess('食物识别成功！已添加到记录中');
      onComplete(result);
      
    } catch (err) {
      const errorMessage = '识别失败，请重试';
      updateState({ error: errorMessage });
      showError(errorMessage);
    } finally {
      updateState({
        isProcessing: false,
        processingStep: ''
      });
    }
  }, [updateState, showSuccess, showError]);

  const stopRecognition = useCallback(() => {
    updateState({
      isProcessing: false,
      processingStep: '',
      error: null
    });
    showInfo('识别已终止');
  }, [updateState, showInfo]);

  const clearError = useCallback(() => {
    updateState({ error: null });
  }, [updateState]);

  return {
    state,
    startRecognition,
    stopRecognition,
    clearError
  };
};
