/**
 * IndexedDB持久化存储服务
 * 提供数据的持久化存储、配额监控和权限管理
 */

export interface StorageQuota {
  used: number;
  total: number;
  percentage: number;
  remaining: number;
}

export interface StorageStatus {
  isPersistent: boolean;
  quota: StorageQuota;
  isSupported: boolean;
}

class IndexedDBService {
  private dbName = 'KcalTrackerDB';
  private dbVersion = 1;
  private db: IDBDatabase | null = null;

  /**
   * 初始化数据库连接
   */
  async initialize(): Promise<void> {
    try {
      // 检查浏览器支持
      if (!this.isSupported()) {
        throw new Error('浏览器不支持IndexedDB');
      }

      // 申请持久化存储权限
      await this.requestPersistentStorage();

      // 打开数据库
      this.db = await this.openDatabase();
      
      console.log('IndexedDB初始化成功');
    } catch (error) {
      console.error('IndexedDB初始化失败:', error);
      throw error;
    }
  }

  /**
   * 检查浏览器是否支持IndexedDB和持久化存储
   */
  isSupported(): boolean {
    return 'indexedDB' in window && 'navigator' in window && 'storage' in navigator;
  }

  /**
   * 申请持久化存储权限
   */
  async requestPersistentStorage(): Promise<boolean> {
    try {
      if ('storage' in navigator && 'persist' in navigator.storage) {
        const isPersistent = await navigator.storage.persist();
        console.log('持久化存储权限:', isPersistent ? '已获取' : '被拒绝');
        return isPersistent;
      }
      return false;
    } catch (error) {
      console.error('申请持久化存储权限失败:', error);
      return false;
    }
  }

  /**
   * 获取存储状态信息
   */
  async getStorageStatus(): Promise<StorageStatus> {
    try {
      const isSupported = this.isSupported();
      let isPersistent = false;
      let quota: StorageQuota = {
        used: 0,
        total: 0,
        percentage: 0,
        remaining: 0
      };

      if (isSupported && 'storage' in navigator) {
        // 检查持久化状态
        if ('persisted' in navigator.storage) {
          isPersistent = await navigator.storage.persisted();
        }

        // 获取存储配额信息
        if ('estimate' in navigator.storage) {
          const estimate = await navigator.storage.estimate();
          const used = estimate.usage || 0;
          const total = estimate.quota || 0;
          const percentage = total > 0 ? Math.round((used / total) * 100) : 0;
          const remaining = total - used;

          quota = {
            used,
            total,
            percentage,
            remaining
          };
        }
      }

      return {
        isPersistent,
        quota,
        isSupported
      };
    } catch (error) {
      console.error('获取存储状态失败:', error);
      return {
        isPersistent: false,
        quota: { used: 0, total: 0, percentage: 0, remaining: 0 },
        isSupported: false
      };
    }
  }

  /**
   * 打开数据库连接
   */
  private openDatabase(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => {
        reject(new Error('无法打开数据库'));
      };

      request.onsuccess = () => {
        resolve(request.result);
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        this.createObjectStores(db);
      };
    });
  }

  /**
   * 创建对象存储
   */
  private createObjectStores(db: IDBDatabase): void {
    // 用户档案存储
    if (!db.objectStoreNames.contains('profiles')) {
      const profileStore = db.createObjectStore('profiles', { keyPath: 'id' });
      profileStore.createIndex('userId', 'userId', { unique: false });
    }

    // 营养记录存储
    if (!db.objectStoreNames.contains('nutritionRecords')) {
      const recordStore = db.createObjectStore('nutritionRecords', { keyPath: 'id' });
      recordStore.createIndex('date', 'date', { unique: false });
      recordStore.createIndex('mealType', 'mealType', { unique: false });
    }

    // 应用设置存储
    if (!db.objectStoreNames.contains('settings')) {
      db.createObjectStore('settings', { keyPath: 'key' });
    }

    console.log('数据库表结构创建完成');
  }

  /**
   * 存储数据
   */
  async setItem<T>(storeName: string, data: T): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.put(data);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error('数据存储失败'));
    });
  }

  /**
   * 获取数据
   */
  async getItem<T>(storeName: string, key: string): Promise<T | null> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.get(key);

      request.onsuccess = () => {
        resolve(request.result || null);
      };
      request.onerror = () => reject(new Error('数据获取失败'));
    });
  }

  /**
   * 获取所有数据
   */
  async getAllItems<T>(storeName: string): Promise<T[]> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.getAll();

      request.onsuccess = () => {
        resolve(request.result || []);
      };
      request.onerror = () => reject(new Error('数据获取失败'));
    });
  }

  /**
   * 删除数据
   */
  async removeItem(storeName: string, key: string): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.delete(key);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error('数据删除失败'));
    });
  }

  /**
   * 清空存储
   */
  async clear(storeName: string): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.clear();

      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error('数据清空失败'));
    });
  }

  /**
   * 检查存储使用率并发出警告
   */
  async checkStorageUsage(): Promise<boolean> {
    try {
      const status = await this.getStorageStatus();
      
      if (status.quota.percentage >= 85) {
        console.warn('存储空间使用率达到85%，建议清理数据');
        return true; // 需要警告
      }
      
      return false;
    } catch (error) {
      console.error('检查存储使用率失败:', error);
      return false;
    }
  }

  /**
   * 关闭数据库连接
   */
  close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}

// 创建单例实例
export const indexedDBService = new IndexedDBService();
