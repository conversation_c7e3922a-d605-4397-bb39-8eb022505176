// 这个脚本用于在浏览器中生成PNG图标
// 在浏览器控制台中运行此代码

function createIcon(size) {
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const ctx = canvas.getContext('2d');
    
    const scale = size / 32;
    
    // 背景渐变
    const bgGradient = ctx.createLinearGradient(0, 0, size, size);
    bgGradient.addColorStop(0, '#10b981');
    bgGradient.addColorStop(1, '#14b8a6');
    
    // 绘制背景圆形
    ctx.fillStyle = bgGradient;
    ctx.beginPath();
    ctx.arc(size/2, size/2, 15 * scale, 0, 2 * Math.PI);
    ctx.fill();
    
    // 绘制盘子
    ctx.fillStyle = 'rgba(255,255,255,0.9)';
    ctx.strokeStyle = '#e5e7eb';
    ctx.lineWidth = 0.5 * scale;
    ctx.beginPath();
    ctx.arc(size/2, 18 * scale, 10 * scale, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();
    
    // 绘制食物元素
    // 主菜
    ctx.fillStyle = 'rgba(34,197,94,0.8)';
    ctx.beginPath();
    ctx.ellipse(14 * scale, 17 * scale, 3 * scale, 2 * scale, 0, 0, 2 * Math.PI);
    ctx.fill();
    
    // 蔬菜
    ctx.fillStyle = 'rgba(34,197,94,0.7)';
    ctx.beginPath();
    ctx.arc(19 * scale, 16 * scale, 1.5 * scale, 0, 2 * Math.PI);
    ctx.fill();
    
    ctx.fillStyle = 'rgba(22,163,74,0.7)';
    ctx.beginPath();
    ctx.arc(20 * scale, 18 * scale, 1.2 * scale, 0, 2 * Math.PI);
    ctx.fill();
    
    // 碳水化合物
    ctx.fillStyle = 'rgba(251,191,36,0.8)';
    ctx.beginPath();
    ctx.ellipse(15 * scale, 20 * scale, 2.5 * scale, 1.5 * scale, 0, 0, 2 * Math.PI);
    ctx.fill();
    
    // AI 元素
    ctx.strokeStyle = 'rgba(255,255,255,0.9)';
    ctx.lineWidth = 0.8 * scale;
    ctx.beginPath();
    ctx.arc(16 * scale, 10 * scale, 3 * scale, 0, 2 * Math.PI);
    ctx.stroke();
    
    // AI 点
    ctx.fillStyle = 'rgba(255,255,255,0.8)';
    const points = [
        [14.5 * scale, 9.5 * scale],
        [16 * scale, 9 * scale],
        [17.5 * scale, 9.5 * scale]
    ];
    
    points.forEach(([x, y]) => {
        ctx.beginPath();
        ctx.arc(x, y, 0.4 * scale, 0, 2 * Math.PI);
        ctx.fill();
    });
    
    // kcal 文字 (只在较大尺寸显示)
    if (size >= 64) {
        ctx.fillStyle = 'rgba(255,255,255,0.9)';
        ctx.font = `bold ${4 * scale}px Arial, sans-serif`;
        ctx.textAlign = 'center';
        ctx.fillText('kcal', size/2, 25 * scale);
    }
    
    return canvas;
}

function downloadIcon(canvas, filename) {
    const link = document.createElement('a');
    link.download = filename;
    link.href = canvas.toDataURL('image/png');
    link.click();
}

// 生成所有尺寸的图标
function generateAllIcons() {
    const sizes = [
        { size: 16, name: 'favicon-16x16.png' },
        { size: 32, name: 'favicon-32x32.png' },
        { size: 64, name: 'pwa-64x64.png' },
        { size: 192, name: 'pwa-192x192.png' },
        { size: 512, name: 'pwa-512x512.png' }
    ];
    
    sizes.forEach(({ size, name }) => {
        const canvas = createIcon(size);
        setTimeout(() => downloadIcon(canvas, name), 100 * sizes.indexOf({ size, name }));
    });
}

// 在控制台运行: generateAllIcons()
console.log('运行 generateAllIcons() 来生成所有图标');
